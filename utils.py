import os, sys
import csv
import pandas as pd
import requests
import json
import time
from loguru import logger

# 读取Excel文件
def read_excel(file_path):
    df = pd.read_excel(file_path)
    return df


# 读取csv文件
def read_csv(file_path):
    df = pd.read_csv(file_path)
    return df

def save_to_csv(data, csv_path, fieldnames):
    """
    将单行数据写入CSV文件
    :param data: 要写入的字典数据
    :param csv_path: CSV 文件路径
    :param fieldnames: CSV 表头字段列表
    """
    file_exists = os.path.exists(csv_path)
    
    with open(csv_path, mode='a', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        if not file_exists or f.tell() == 0:
            writer.writeheader()
        writer.writerow(data)


def convert_csv_to_excel(csv_path, excel_path):
    df = pd.read_csv(csv_path)
    df.to_excel(excel_path, index=False)
    print(f"已将 {csv_path} 转换为 {excel_path}")



# 获取工作流响应
def get_work_flow(text, app_id):
    DASHSCOPE_API_KEY = "sk-fb36d99bbb4c4c25bc6c3306937e980b"
    # YOUR_APP_ID = "74958984963246bfbf19d58b596a62df"
    # YOUR_APP_ID = "c47f6d4498ec4406a007cc5534a996c6"
    url = f"https://dashscope.aliyuncs.com/api/v1/apps/{app_id}/completion"

    headers = {
        "Authorization": f"Bearer {DASHSCOPE_API_KEY}",
        "Content-Type": "application/json"
    } 

    payload = {
        "input": {
            "messages":[
                {"role":"user","content":text},
            ]
        },
    }
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    logger.debug(f"response.json(): {response.json()}")
    result_text = response.json().get("output", {}).get("text", "")
    try:
        result_json = json.loads(result_text)
    except json.JSONDecodeError:
        result_json = {}
    return result_json