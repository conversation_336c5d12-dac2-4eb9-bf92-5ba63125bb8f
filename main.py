import json
from datetime import datetime, timedelta
from typing import Dict, List

import a
from pack1 import bcdef
import bson

class MyClass(object):

    def __init__(self, a: int, b: int):
        self.a = a
        self.b = b

    def __add__(self, other):
        agg = MyClass(0 ,0)
        agg.a = self.a + other.a
        agg.b = self.b + other.b
        return agg


def print_hi(name):
    print(f'Hi, {name}')  # Press Ctrl+F8 to toggle the breakpoint.

def potential_before_open_calls(start_datetime: datetime, end_datetime: datetime, configDetails: Dict,
                                parsed_call_history: List[Dict]):
    potential_calls = []
    now = datetime.now().replace(second=0, microsecond=0)
    # 营业前拨打记录
    calls_before = [call for call in parsed_call_history if call['call_time_type'] == 'beforeOpen']
    calls_before_count = len(calls_before)
    if calls_before_count < configDetails['beforeOpen']['callLimit']:
        # 尚未达到 beforeOpen 的拨打限制
        before_open_offset = configDetails['beforeOpen']['timeOffset']
        before_open_start = start_datetime + timedelta(minutes=before_open_offset)
        before_open_end = start_datetime

        if calls_before_count == 0:
            # 遍历拨打次数,将潜在拨打时间加入列表
            for i in range(configDetails['beforeOpen']['callLimit']):
                proposed_call_time = before_open_start + timedelta(
                    minutes=i * configDetails['beforeOpen']['callInterval'])
                # 拨打时间大于当前时间并且小于等于结束时间
                if proposed_call_time > now and proposed_call_time <= before_open_end:
                    potential_calls.append({'call_time_type': 'beforeOpen', 'proposed_call_time': proposed_call_time})
        else:
            # 最后一次拨打时间 + call_interval
            last_call = max(call['call_at'] for call in calls_before)
            proposed_call_time = last_call + timedelta(minutes=configDetails['beforeOpen']['callInterval'])
            # 拨打时间大于当前时间并且小于等于结束时间
            if proposed_call_time > now and proposed_call_time <= before_open_end:
                potential_calls.append({'call_time_type': 'beforeOpen', 'proposed_call_time': proposed_call_time})

    return potential_calls


# 营业中潜在拨打时间
def potential_during_open_calls(start_datetime: datetime, end_datetime: datetime, configDetails: Dict,
                                parsed_call_history: List[Dict]):
    potential_calls = []
    now = datetime.now().replace(second=0, microsecond=0)
    # 营业中拨打记录
    calls_during = [call for call in parsed_call_history if call['call_time_type'] == 'duringOpen']
    calls_during_count = len(calls_during)
    if calls_during_count < configDetails['duringOpen']['callLimit']:
        # 尚未达到 duringOpen 的拨打限制
        during_open_offset = configDetails['duringOpen']['timeOffset']
        during_open_start = start_datetime + timedelta(minutes=during_open_offset)
        during_open_end = end_datetime

        # 计算下一次拨打时间
        if calls_during_count == 0:
            # 尚未拨打过，下一次拨打时间为当前时间或时间段开始时间的较晚者
            proposed_call_time = max(now, during_open_start)
        else:
            # 最后一次拨打时间 + call_interval
            last_call = max(call['call_at'] for call in calls_during)
            proposed_call_time = last_call + timedelta(minutes=configDetails['duringOpen']['callInterval'])
            # 确保拨打时间不早于当前时间
            proposed_call_time = max(proposed_call_time, now)

        if proposed_call_time < during_open_end:
            # 拨打时间不超出当前时间段，加入潜在拨打时间列表
            potential_calls.append({
                'call_time_type': 'duringOpen',
                'proposed_call_time': proposed_call_time
            })
    return potential_calls


if __name__ == '__main__':
    # print(bson.__file__)
    # c1 = MyClass(1,1)
    # c2 = MyClass(2,2)
    # c3 = c1 + c2
    # print(c1)
    # print(c2)
    # print(c3)
    # print(c3.__dict__)
    #
    # something = c3.__dict__
    # print(type(something))
    #
    # json_str = json.dumps(c3.__dict__)
    # print(json_str.__class__)
    # print_hi('PyCharm')
    # print(a.m1(1))
    # print(bcdef.m1(1))

    # arr = [1,2,3,4,5]
    # collection = {1,1,2}
    # for x in arr:
    #     print(x)
    # print(collection)

    # numbers = [1, 2, 3, 4, 5]
    # squared_even_numbers = [x ** 2 for x in numbers if x % 2 == 0]
    # print(squared_even_numbers)  # 输出: [4, 16]

    from functools import reduce

    # numbers = [1, 2, 3, 4, 5]

    # Map: 每个元素乘以2
    # mapped = map(lambda x: x * 2, numbers)
    # l = list(mapped)
    # print(l.__class__)
    #
    # print(numbers.__class__)
    # print(mapped.__class__)

    # # Filter: 过滤偶数
    # filtered = filter(lambda x: x % 2 == 0, mapped)
    #
    # # Reduce: 求和
    # result = reduce(lambda x, y: x + y, filtered)
    # print(result)  # 输出: 12
    parsed_call_history = []

    configDetails = {
            "duringOpen": {
                "callLimit": 3,
                "timeOffset": 20,
                "callInterval": 30
            },
            "beforeOpen": {
                "callLimit": 1,
                "timeOffset": -30,
                "callInterval": 30
            }
        }

    default_business_hours = [{"days": [1, 2, 3, 4, 5, 6, 0], "start_time": "10:00", "end_time": "22:00", "cross_day": False}]
    dialHours = default_business_hours

    now = datetime.now().replace(second=0, microsecond=0)
    # # 用户的星期表示：0=星期天, 1=星期一, ..., 6=星期六
    user_weekday = (now.weekday() + 1) % 7

    days_ahead = 0
    while days_ahead <= 7:
        next_day_weekday = (user_weekday + days_ahead) % 7
        next_day_date = now.date() + timedelta(days=days_ahead)
        next_day_dh = [dh for dh in dialHours if next_day_weekday in dh['days']]

        potential_calls = []
        remaining_before_calls = 5
        remaining_during_calls = 5
        for dh in next_day_dh:
            try:
                start_time_obj = datetime.strptime(dh['start_time'], "%H:%M").time()
                end_time_obj = datetime.strptime(dh['end_time'], "%H:%M").time()
            except ValueError as e:
                print(f"错误: 无法解析拨打时间段的时间格式。{e}")
                continue  # 跳过这个拨打时间段
            start_datetime = datetime.combine(next_day_date, start_time_obj)
            if dh['cross_day'] and end_time_obj <= start_time_obj:
                end_datetime = datetime.combine(next_day_date + timedelta(days=1), end_time_obj)
            else:
                end_datetime = datetime.combine(next_day_date, end_time_obj)

            # 计算营业前前潜在拨打时间
            if remaining_before_calls > 0:
                potential_calls += potential_before_open_calls(start_datetime, end_datetime, configDetails, parsed_call_history)
            # 计算营业中潜在拨打时间
            if remaining_during_calls > 0:
                potential_calls += potential_during_open_calls(start_datetime, end_datetime, configDetails, parsed_call_history)

            # 如果有潜在的拨打时间，选择最早的一个
        if potential_calls:
            # 按 proposed_call_time 排序，选择最早的
            next_potential_call = min(potential_calls, key=lambda x: x['proposed_call_time'])
            next_call_time_dt = next_potential_call['proposed_call_time']
            call_time_type = next_potential_call['call_time_type']

            # 检查是否达到拨打限制
            if_end_call = (0 + 1) >= \
                        (configDetails['beforeOpen']['callLimit'] + configDetails['duringOpen']['callLimit'])

            # 将 next_call_time 转换为字符串格式
            next_call_time_str = next_call_time_dt.strftime("%Y-%m-%d %H:%M:%S")
            print(next_call_time_str, call_time_type, if_end_call)
        days_ahead += 1


