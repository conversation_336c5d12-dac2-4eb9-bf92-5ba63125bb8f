import json

datas = json.loads('''
[
    {
        "轮数": 1,
        "并发": 5,
        "问答次数": 25,
        "测试时间": "2025-08-01 19:12:45",
        "客户端首token(阿里云->网关->tripnow) avg": 1777.2,
        "服务端首token avg": 1510.71,
        "参数提取模型 avg": 373.8,
        "知识库 avg": 383.6,
        "总结模型首token avg": 695.17,
        "连网搜索额外耗时 avg": null,
        "客户端首token(阿里云->网关->tripnow) p80": 2002.57,
        "服务端首token p80": 1719.8,
        "参数提取模型 p80": 582.4,
        "知识库 p80": 427.4,
        "总结模型首token p80": 896.4,
        "连网搜索额外耗时 p80": null,
        "客户端首token(阿里云->网关->tripnow) p90": 2054.47,
        "服务端首token p90": 1820.7,
        "参数提取模型 p90": 792.8,
        "知识库 p90": 459.2,
        "总结模型首token p90": 1050.5,
        "连网搜索额外耗时 p90": null
    },
    {
        "轮数": 2,
        "并发": 5,
        "问答次数": 25,
        "测试时间": "2025-08-01 19:12:45",
        "客户端首token(阿里云->网关->tripnow) avg": 1692.26,
        "服务端首token avg": 1461.72,
        "参数提取模型 avg": 320.36,
        "知识库 avg": 395.64,
        "总结模型首token avg": 696.64,
        "连网搜索额外耗时 avg": null,
        "客户端首token(阿里云->网关->tripnow) p80": 1911.38,
        "服务端首token p80": 1695.6,
        "参数提取模型 p80": 427.4,
        "知识库 p80": 444.0,
        "总结模型首token p80": 880.8,
        "连网搜索额外耗时 p80": null,
        "客户端首token(阿里云->网关->tripnow) p90": 2172.23,
        "服务端首token p90": 1924.0,
        "参数提取模型 p90": 518.4,
        "知识库 p90": 446.4,
        "总结模型首token p90": 1013.2,
        "连网搜索额外耗时 p90": null
    },
    {
        "轮数": 3,
        "并发": 5,
        "问答次数": 25,
        "测试时间": "2025-08-01 19:12:45",
        "客户端首token(阿里云->网关->tripnow) avg": 1538.0,
        "服务端首token avg": 1267.6,
        "参数提取模型 avg": 267.76,
        "知识库 avg": 365.28,
        "总结模型首token avg": 583.12,
        "连网搜索额外耗时 avg": null,
        "客户端首token(阿里云->网关->tripnow) p80": 1693.38,
        "服务端首token p80": 1392.6,
        "参数提取模型 p80": 302.8,
        "知识库 p80": 413.2,
        "总结模型首token p80": 682.4,
        "连网搜索额外耗时 p80": null,
        "客户端首token(阿里云->网关->tripnow) p90": 1792.4,
        "服务端首token p90": 1491.6,
        "参数提取模型 p90": 429.8,
        "知识库 p90": 444.8,
        "总结模型首token p90": 707.8,
        "连网搜索额外耗时 p90": null
    },
    {
        "轮数": 4,
        "并发": 5,
        "问答次数": 25,
        "测试时间": "2025-08-01 19:12:45",
        "客户端首token(阿里云->网关->tripnow) avg": 1662.71,
        "服务端首token avg": 1414.04,
        "参数提取模型 avg": 287.56,
        "知识库 avg": 373.92,
        "总结模型首token avg": 703.56,
        "连网搜索额外耗时 avg": null,
        "客户端首token(阿里云->网关->tripnow) p80": 1931.59,
        "服务端首token p80": 1679.2,
        "参数提取模型 p80": 384.0,
        "知识库 p80": 432.2,
        "总结模型首token p80": 943.8,
        "连网搜索额外耗时 p80": null,
        "客户端首token(阿里云->网关->tripnow) p90": 2179.78,
        "服务端首token p90": 1917.6,
        "参数提取模型 p90": 479.6,
        "知识库 p90": 443.2,
        "总结模型首token p90": 1147.6,
        "连网搜索额外耗时 p90": null
    },
    {
        "轮数": 5,
        "并发": 5,
        "问答次数": 25,
        "测试时间": "2025-08-01 19:12:45",
        "客户端首token(阿里云->网关->tripnow) avg": 1733.93,
        "服务端首token avg": 1498.84,
        "参数提取模型 avg": 336.64,
        "知识库 avg": 383.52,
        "总结模型首token avg": 727.72,
        "连网搜索额外耗时 avg": null,
        "客户端首token(阿里云->网关->tripnow) p80": 1888.57,
        "服务端首token p80": 1672.6,
        "参数提取模型 p80": 488.6,
        "知识库 p80": 439.8,
        "总结模型首token p80": 867.4,
        "连网搜索额外耗时 p80": null,
        "客户端首token(阿里云->网关->tripnow) p90": 2403.58,
        "服务端首token p90": 2185.8,
        "参数提取模型 p90": 546.8,
        "知识库 p90": 455.6,
        "总结模型首token p90": 1085.4,
        "连网搜索额外耗时 p90": null
    }
]
''')

# 字段定义
base_keys = ["并发", "轮数", "测试时间", "问答次数"]
metric_prefixes = [
    "客户端首token(阿里云->网关->tripnow)",
    "服务端首token",
    "参数提取模型",
    "知识库",
    "总结模型首token",
    "连网搜索额外耗时"
]

# 表头
headers = base_keys + metric_prefixes

# 拼接每个指标的内容
def format_metric(name, item):
    avg = item.get(f"{name} avg")
    p90 = item.get(f"{name} p90")
    p80 = item.get(f"{name} p80")
    if avg is None and p90 is None and p80 is None:
        return ""
    return f"(avg {avg}) (p90 {p90}) (p80 {p80})"

# 构建行
print(",".join(headers))
for item in datas:
    row = [str(item.get(k, "")) for k in base_keys]
    row += [format_metric(name, item) for name in metric_prefixes]
    print(",".join(row))
