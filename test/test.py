#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TripNow 性能统计脚本 (含连网搜索耗时)
修改点：
1. 新增全局变量 ROUNDS
2. fetch_first_token_and_nodes(msg, session_id)
3. 每线程跑完 ROUNDS 次后换新的 session_id
4. 结果按轮数分组统计，输出 result 数组
"""
import json, uuid, time, datetime as dt
from concurrent.futures import ThreadPoolExecutor, as_completed

import numpy as np, requests


# =============== 基本配置 ===============
CHAT_URL = "https://t.rsscc.com/pcg/gateway/agi/gpt/gouwuche"
DEV_URL  = "http://***********:14414?cmd=dev"

HEADERS = {  # 略，保持不变
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "authorization": "Basic ODM2QUUxRURDRTZGQjI4QkU4QjE0MTU1NjdGNTBDQkE=",
    "origin": "https://wtest.133.cn",
    "priority": "u=1, i",
    "referer": "https://wtest.133.cn/",
    "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "cross-site",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
}

PROXIES = {  # 若无需代理，直接设为 None
    "http": "http://120.133.0.173:4780",
    "https": "http://120.133.0.173:4780"
}
# PROXIES = None

THREAD_COUNT = 5   # 并发线程数
ROUNDS       = 5   # ⭐ 每个 session 要跑的轮数

MSG_LIST = [
    "机场安检时携带爽肤水可以吗？","5岁儿童坐火车票需要提供什么证明材料吗？","我有2个24寸的箱子，能上飞机吗？","超重5公斤的行李收多少钱？","高铁座位等级怎么选？二等座和一等座服务有什么区别？"
]

# =============== 工具函数 ===============
def _iso_to_dt(s: str) -> dt.datetime | None:
    try:
        return dt.datetime.fromisoformat(s) if s else None
    except ValueError:
        return None

def _ms(delta: dt.timedelta | None):
    return round(delta.total_seconds() * 1000, 3) if delta else None

def fetch_first_token_and_nodes(msg: str, session_id: str) -> dict:
    traceid = str(uuid.uuid4())
    params = {
        "hlid": "hlJ1NHKt",
        "hlsecret": "O5ryOG21",
        "projectver": "1.0.0",
        "modulecver": "8.7.7",
        "uid": "H5mHHyK3FKyD4tbK5SPukOWH",
        "uuid": "H5mHHyK3FKyD4tbK5SPukOWH",
        "systemtime": "1753777247200",
        "hlp": '{"operasys":"windows","sysver":"10","devicetype":"web","device":"unknown unknown","root":"0","linkmode":"wifi","screen":"1463,867,1.4"}',
        "tmc": "",
        "userChannel": "",
        "pid": "250999",
        "cmd": "aichat",
        "sessionId": session_id,     # ⭐ 写入随机 uuid
        "msg": msg,
        "citycode": "",
        "customer_question": "",
        "audio_stream": "false",
        "dataStream": "1",
        "domain": "tmall",
        "gtla": "undefined",
        "gtlo": "undefined",
        "fromsource": "",
        "sid": "02A37654",
        "traceid": traceid,
        "onlineSearch": "0",
    }

    # (1) 客户端首 token
    start = time.time()
    client_ms = None
    try:
        with requests.post(CHAT_URL, params=params, headers=HEADERS,
                           stream=True, timeout=15, proxies=PROXIES) as r:
            r.raise_for_status()
            content = ''
            for chunk in r.iter_lines(decode_unicode=True):
                content += chunk
                if chunk and client_ms is None:
                    client_ms = (time.time() - start) * 1000
            print(content)
    except Exception as e:
        print(f"[{traceid}] chat error: {e}")
        return {}

    # (2) dev timeNodes
    server_ms = param_ms = kb_ms = summary_ms = online_ms = None
    try:
        dev = requests.post(DEV_URL, data={"traceid": traceid},
                            timeout=10, proxies=PROXIES)
        dev.raise_for_status()
        tn = dev.json().get("timeNodes", {})
        if tn:
            t_begin  = _iso_to_dt(tn.get("begin"))
            t_stream = _iso_to_dt(tn.get("streamFirstMsg"))

            t_param_b = _iso_to_dt(tn.get("ModelFlowServiceCallStart"))
            t_param_e = _iso_to_dt(tn.get("ModelFlowServiceCallFinished"))

            t_kb_b = _iso_to_dt(tn.get("chain_TmallDocSearchChainService_b"))
            t_kb_e = _iso_to_dt(tn.get("chain_TmallDocSearchChainService_e"))

            t_online_b = _iso_to_dt(tn.get("waitOnlineSearchB"))
            t_online_e = _iso_to_dt(tn.get("waitOnlineSearchE"))

            server_ms  = _ms(t_stream - t_begin)   if t_stream and t_begin else None
            param_ms   = _ms(t_param_e - t_param_b) if t_param_b and t_param_e else None
            kb_ms      = _ms(t_kb_e - t_kb_b)       if t_kb_b and t_kb_e else None
            summary_ms = _ms(t_stream - t_kb_e)     if t_stream and t_kb_e else None
            online_ms  = _ms(t_online_e - t_online_b) if t_online_b and t_online_e else None
    except Exception as e:
        print(f"[{traceid}] dev error: {e}")

    return {
        "client_ms":  client_ms,
        "server_ms":  server_ms,
        "param_ms":   param_ms,
        "kb_ms":      kb_ms,
        "summary_ms": summary_ms,
        "online_ms":  online_ms
    }

def _stats(vals):
    if not vals:
        return (None, None, None)
    arr = np.array(vals)
    return (round(arr.mean(), 2),
            round(np.percentile(arr, 80), 2),
            round(np.percentile(arr, 90), 2))

# =============== 线程 Worker ===============
def thread_worker():
    sid = str(uuid.uuid4())
    all_results = []
    for r in range(1, ROUNDS + 1):
        for msg in MSG_LIST:
            d = fetch_first_token_and_nodes(msg, sid)
            if d:
                d["round"] = r
                all_results.append(d)
        # 这轮结束后换新的 sessionId
        sid = str(uuid.uuid4())
    return all_results

# =============== 主流程 ===============
def main():
    print(f"\n并发线程: {THREAD_COUNT},  轮数/线程: {ROUNDS}\n")

    # 为每轮准备容器
    round_metrics = {
        r: {"client": [], "server": [], "param": [],
            "kb": [], "summary": [], "online": []}
        for r in range(1, ROUNDS + 1)
    }

    with ThreadPoolExecutor(max_workers=THREAD_COUNT) as ex:
        futs = [ex.submit(thread_worker) for _ in range(THREAD_COUNT)]
        for f in as_completed(futs):
            for d in f.result():
                r = d["round"]
                if d.get("client_ms")  is not None: round_metrics[r]["client"].append(d["client_ms"])
                if d.get("server_ms")  is not None: round_metrics[r]["server"].append(d["server_ms"])
                if d.get("param_ms")   is not None: round_metrics[r]["param"].append(d["param_ms"])
                if d.get("kb_ms")      is not None: round_metrics[r]["kb"].append(d["kb_ms"])
                if d.get("summary_ms") is not None: round_metrics[r]["summary"].append(d["summary_ms"])
                if d.get("online_ms")  is not None: round_metrics[r]["online"].append(d["online_ms"])

    # 输出数组，每轮一条 dict
    results = []
    for r in range(1, ROUNDS + 1):
        client_avg, client_p80, client_p90   = _stats(round_metrics[r]["client"])
        server_avg, server_p80, server_p90   = _stats(round_metrics[r]["server"])
        param_avg,  param_p80,  param_p90    = _stats(round_metrics[r]["param"])
        kb_avg,     kb_p80,     kb_p90       = _stats(round_metrics[r]["kb"])
        summary_avg,summary_p80,summary_p90  = _stats(round_metrics[r]["summary"])
        online_avg, online_p80, online_p90   = _stats(round_metrics[r]["online"])

        total_cnt = len(round_metrics[r]["client"])

        results.append({
            "轮数": r,
            "并发": THREAD_COUNT,
            "问答次数": total_cnt,
            "测试时间": dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),

            "客户端首token(阿里云->网关->tripnow) avg": client_avg,
            "服务端首token avg": server_avg,
            "参数提取模型 avg": param_avg,
            "知识库 avg": kb_avg,
            "总结模型首token avg": summary_avg,
            "连网搜索额外耗时 avg": online_avg,

            "客户端首token(阿里云->网关->tripnow) p80": client_p80,
            "服务端首token p80": server_p80,
            "参数提取模型 p80": param_p80,
            "知识库 p80": kb_p80,
            "总结模型首token p80": summary_p80,
            "连网搜索额外耗时 p80": online_p80,

            "客户端首token(阿里云->网关->tripnow) p90": client_p90,
            "服务端首token p90": server_p90,
            "参数提取模型 p90": param_p90,
            "知识库 p90": kb_p90,
            "总结模型首token p90": summary_p90,
            "连网搜索额外耗时 p90": online_p90
        })

    print("\n===== 统计结果（分轮） =====\n")
    print(json.dumps(results, ensure_ascii=False, indent=4))


if __name__ == "__main__":
    main()
