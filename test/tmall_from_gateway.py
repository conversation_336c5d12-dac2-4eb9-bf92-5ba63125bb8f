#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TripNow 性能统计脚本 (含连网搜索耗时)
"""
import json, uuid, time, datetime as dt
from concurrent.futures import ThreadPoolExecutor, as_completed

import numpy as np, requests


# ================== 基本配置 ==================
CHAT_URL = "https://t.rsscc.com/pcg/gateway/agi/gpt/gouwuche"
DEV_URL = "http://***********:14414?cmd=dev"

HEADERS = {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "authorization": "Basic ODM2QUUxRURDRTZGQjI4QkU4QjE0MTU1NjdGNTBDQkE=",
    "origin": "https://wtest.133.cn",
    "priority": "u=1, i",
    "referer": "https://wtest.133.cn/",
    "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "cross-site",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

PROXIES = {
    "http": "http://120.133.0.173:4780",
    "https": "http://120.133.0.173:4780"
}
# PROXIES = None   # 如不需要代理请取消注释

THREAD_COUNT = 1     # 并发线程数

MSG_LIST = [
    "南航怎么样"
]


# ================== 工具函数 ==================
def _iso_to_dt(s: str) -> dt.datetime | None:
    try:
        return dt.datetime.fromisoformat(s) if s else None
    except ValueError:
        return None


def _ms(delta: dt.timedelta | None):
    return round(delta.total_seconds() * 1000, 3) if delta else None


def fetch_first_token_and_nodes(msg: str) -> dict:
    traceid = str(uuid.uuid4())
    params = {
        "hlid": "hlJ1NHKt",
        "hlsecret": "O5ryOG21",
        "projectver": "1.0.0",
        "modulecver": "8.7.7",
        "uid": "H5mHHyK3FKyD4tbK5SPukOWH",
        "uuid": "H5mHHyK3FKyD4tbK5SPukOWH",
        "systemtime": "1753777247200",
        "hlp": '{"operasys":"windows","sysver":"10","devicetype":"web","device":"unknown unknown","root":"0","linkmode":"wifi","screen":"1463,867,1.4"}',
        "tmc": "",
        "userChannel": "",
        "pid": "250999",
        "cmd": "aichat",
        "sessionId": "",
        "msg": msg,
        "citycode": "",
        "customer_question": "",
        "audio_stream": "false",
        "dataStream": "1",
        "domain": "tmall",
        "gtla": "undefined",
        "gtlo": "undefined",
        "fromsource": "",
        "sid": "02A37654",
        "traceid": traceid,
        "onlineSearch": "1",
    }

    # 1) 客户端首 token
    start = time.time()
    client_ms = None
    try:
        with requests.post(CHAT_URL, params=params, headers=HEADERS,
                           stream=True, timeout=15) as r:
            r.raise_for_status()
            content = ""
            for chunk in r.iter_content(chunk_size=1, decode_unicode=True):
                content += chunk
                if chunk and client_ms is None:
                    client_ms = (time.time() - start) * 1000
            print(content)
    except Exception as e:
        print(f"[{traceid}] chat error: {e}")
        return {}

    # 2) dev timeNodes
    server_ms = param_ms = kb_ms = summary_ms = online_ms = None  # --- 新增 online_ms
    try:
        dev = requests.post(DEV_URL, data={"traceid": traceid}, timeout=10)
        dev.raise_for_status()
        tn = dev.json().get("timeNodes", {})
        if tn:
            t_begin = _iso_to_dt(tn.get("begin"))
            t_stream = _iso_to_dt(tn.get("streamFirstMsg"))

            t_param_b = _iso_to_dt(tn.get("ModelFlowServiceCallStart"))
            t_param_e = _iso_to_dt(tn.get("ModelFlowServiceCallFinished"))

            t_kb_b = _iso_to_dt(tn.get("chain_TmallDocSearchChainService_b"))
            t_kb_e = _iso_to_dt(tn.get("chain_TmallDocSearchChainService_e"))

            t_online_b = _iso_to_dt(tn.get("waitOnlineSearchB"))      # --- 新增
            t_online_e = _iso_to_dt(tn.get("waitOnlineSearchE"))      # --- 新增

            server_ms = _ms(t_stream - t_begin) if t_stream and t_begin else None
            param_ms = _ms(t_param_e - t_param_b) if t_param_b and t_param_e else None
            kb_ms = _ms(t_kb_e - t_kb_b) if t_kb_b and t_kb_e else None
            summary_ms = _ms(t_stream - t_kb_e) if t_stream and t_kb_e else None
            online_ms = _ms(t_online_e - t_online_b) if t_online_b and t_online_e else None  # --- 新增
    except Exception as e:
        print(f"[{traceid}] dev error: {e}")

    return {
        "client_ms": client_ms,
        "server_ms": server_ms,
        "param_ms": param_ms,
        "kb_ms": kb_ms,
        "summary_ms": summary_ms,
        "online_ms": online_ms        # --- 新增
    }


def _stats(vals):
    if not vals:
        return (None, None, None)
    arr = np.array(vals)
    return (round(arr.mean(), 2),
            round(np.percentile(arr, 80), 2),
            round(np.percentile(arr, 90), 2))


# ================== 主流程 ==================
def main():
    print(f"\nRunning {THREAD_COUNT} concurrent requests ...\n")

    client_l, server_l, param_l, kb_l, summary_l, online_l = [], [], [], [], [], []  # --- online_l

    with ThreadPoolExecutor(max_workers=THREAD_COUNT) as ex:
        futs = [ex.submit(fetch_first_token_and_nodes, m) for m in MSG_LIST]
        for f in as_completed(futs):
            d = f.result()
            if not d:
                continue
            if d.get("client_ms") is not None:  client_l.append(d["client_ms"])
            if d.get("server_ms") is not None:  server_l.append(d["server_ms"])
            if d.get("param_ms")  is not None:  param_l.append(d["param_ms"])
            if d.get("kb_ms")     is not None:  kb_l.append(d["kb_ms"])
            if d.get("summary_ms")is not None:  summary_l.append(d["summary_ms"])
            if d.get("online_ms") is not None:  online_l.append(d["online_ms"])  # --- 新增

    client_avg, client_p80, client_p90   = _stats(client_l)
    server_avg, server_p80, server_p90   = _stats(server_l)
    param_avg,  param_p80,  param_p90    = _stats(param_l)
    kb_avg,     kb_p80,     kb_p90       = _stats(kb_l)
    summary_avg,summary_p80,summary_p90  = _stats(summary_l)
    online_avg, online_p80, online_p90   = _stats(online_l)   # --- 新增
    total_count = len(client_l)
    result = {
        "并发": THREAD_COUNT,
        "问答次数": total_count,
        "测试时间": dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),

        "客户端首token(阿里云->网关->tripnow) avg": client_avg,
        "服务端首token avg": server_avg,
        "参数提取模型 avg": param_avg,
        "知识库 avg": kb_avg,
        "总结模型首token avg": summary_avg,
        "连网搜索额外耗时 avg": online_avg,                     # --- 新增

        "客户端首token(阿里云->网关->tripnow) p80": client_p80,
        "服务端首token p80": server_p80,
        "参数提取模型 p80": param_p80,
        "知识库 p80": kb_p80,
        "总结模型首token p80": summary_p80,
        "连网搜索额外耗时 p80": online_p80,                     # --- 新增

        "客户端首token(阿里云->网关->tripnow) p90": client_p90,
        "服务端首token p90": server_p90,
        "参数提取模型 p90": param_p90,
        "知识库 p90": kb_p90,
        "总结模型首token p90": summary_p90,
        "连网搜索额外耗时 p90": online_p90                      # --- 新增
    }

    print("\n===== 统计结果 =====\n")
    print(json.dumps(result, ensure_ascii=False, indent=4))


if __name__ == "__main__":
    # main()
    # print(_stats([2304.137707,2378.335476,2307.109594,2543.335438,2445.956469,1183.252811,1581.995487,1492.184877,1366.835117,2207.658768,2290.685415,1797.538996,1469.361782,1424.672365,2109.699965,2086.886883,2498.611212,1745.665073,1634.410143,1697.43681,1602.010727,1382.475853,2038.093567,1916.614056,1604.26259,1599.323034,1707.406282,1574.769258,1619.124889,1812.454224,1904.954672,2683.533669,2092.629671,1602.6721,1829.118013,1291.113853,1688.191652,1514.101505,1400.838375,1701.931238,2107.968569,2027.825832,1583.159685,1498.236418,1477.596045,1673.068047,1583.09865,1661.967039,1538.660288,1657.43947,1658.496857,1749.106884,2126.127005,2548.585892,1446.302891,1579.837084,1989.403009,1812.638998,2117.422342,1970.709562,1807.231188,1360.321999,1952.470541,1731.878519,2243.601561,1759.434223,1709.591627,2107.657909,1428.736925,1459.477663,1896.948576,1607.087135,1984.149456,1592.074394,1500.261784,1426.298618,2420.006275,1422.630548,1561.686993,2208.693266,1344.725609,1712.507248,1606.520176,1872.173309,1544.705629,1710.197687,1945.924044,1371.550322,1501.347065,1326.286077,1409.34968,1300.843,1724.966049,1824.44334,1993.200064,1895.310879,1685.611248,2061.574697,2494.970322,1632.600069,1535.212517,1693.36319,1622.384071,1339.909315,1501.538277,1806.160688,2331.429482,1503.916025,1833.44245,2302.537918,1519.360781,1529.09255,1517.792225,1919.918776,1517.616749,1393.445015,1456.768274,1577.844143,1821.511507,1617.347956,1540.099859,1829.727888,1553.534031,1584.931374,1633.513451,1540.940762,1490.742207,1551.630735,1710.295916,1440.083027,1490.314245,1663.671255,1540.687084,1684.212685,1508.773565,1654.151917,1471.305847,1601.843357,1688.67135,1777.920485,1596.434355,2103.256226,1928.56431,1361.919641,1547.632694,1395.146132,1658.45871,1710.466862,1544.425249,2573.329449,1588.524103,1589.71262,1827.73447,2271.870613,1770.721436,1799.68977,1593.523026,1981.658697,1686.343431,1574.2836,1185.82058,1826.015472,1468.390703,1405.879736,1768.19396,1367.9986,1444.314241,1699.534893,2161.598682,1905.717134,1621.154547,1461.11393,1682.089806,1483.206272,1425.303459,1732.54323,1571.839809,1836.980104,1678.357363,1637.560129,1463.032007,1947.874546,1711.660147,1943.99929,1501.745224,1665.331841,1409.478188,1714.112759,1598.395348,1567.749262,1422.791243,2099.937916,1911.518335,1428.003311,1785.496712,1877.819061,2146.057606,2000.696182,1402.955294,1743.191957,1646.689415,1878.76606,2019.376993,1199.804306,1588.879585,1486.07111,1539.663553,1647.197485,1781.51226,2575.444698,1654.139042,2037.770033,1399.788618,1391.163826,1811.489582,1505.609274,1987.102747,1626.66297,1525.08235,1970.901489,1602.026224,1572.021484,1894.466877,1634.414196,1738.919497,1511.642933,1478.852987,1593.88423,1720.300913,1647.068262,1371.920586,2931.447268,1680.853367,1435.492992,1322.923183,1306.877136,1487.689257,1580.450058,1595.889091,1850.45886,1721.912861,2485.673666,2029.762745,1967.951536,2262.031794,1635.211468,1822.326422,2114.255428]))
    # print(_stats([[1478.32942,2039.605618,2531.680584,2043.993235,2551.689386,1571.575165,1659.394503,1503.043652,1580.319166,1676.213741,2097.410679,2225.143433,1749.115229,1558.530331,2606.998444,1643.849611,1460.574389,1596.554041,2062.776566,2266.388655,1554.368258,1345.601559,1505.90539,1680.971861,1630.926847,1404.943943,1519.212246,2171.354532,1451.399326,1532.68981,3111.153364,2026.606083,1644.62924,2103.681326,1574.542522,1560.780048,1863.14249,1743.591309,1631.396532,1886.030436,2077.931881,1600.033998,2062.705994,1588.350534,1756.932259,1518.05377,1376.393557,1889.105082,1502.684832,1668.134451,1684.105873,2160.068035,1682.272196,1601.721287,1714.95223,1739.824057,2118.503332,2084.227562,1736.184835,1823.359489,1648.044348,1694.800138,1683.431149,1607.572556,2110.776663,2465.158939,1328.298092,2398.434639,1514.305592,1468.744516,1707.468748,2082.756042,1357.270956,1666.672707,1462.209463,2423.114538,2249.382257,2016.675711,1874.911547,1710.40988,1701.937199,1616.347313,1764.99176,1470.644951,2144.276381,2356.970549,1982.388735,1711.175919,1329.197645,1699.127913,1704.414845,1396.814823,1554.314137,1667.374849,2285.416126,1677.663565,1902.486324,1710.43992,1772.856236,1696.837902,1828.499317,2269.621372,1577.129602,1399.788857,1422.482491,2122.074604,1977.956772,1713.497639,2056.861639,1450.114489,1580.41358,2013.693094,2060.449839,1548.478603,1847.214699,2317.894936,1402.131319,1876.684427,1778.620481,1696.400404,1738.072395,1536.812544,1907.875776,1865.678787,1708.247423,1554.507971,1361.613035,1716.30311,1733.370543,2840.700626,1598.852873,1482.10001,1869.523525,1752.587557,1500.523806,1982.499838,1287.83989,1532.94611,2069.452047,1717.546701,1750.016451,1848.93012,1732.198,2282.857895,1807.688713,1498.247385,2108.603954,1953.516245,1829.318762,2198.882341,1584.341764,1951.588154,2894.401789,2003.096342,1567.056179,1861.108541,2459.27,1627.603769,1938.807249,1738.608837,1563.841105,2034.137487,1730.7868,2694.57221,2115.232229,1608.702183,1725.318193,1865.801096,1620.238543,1951.844931,2181.477308,1334.368467,1536.534548,1982.560158,2264.643431,1661.789656,1651.017427,1915.374756,1587.871552,1664.823532,1719.106674,2604.928732,1601.651192,1539.213657,1739.279747,1413.27095,1985.240459,1621.354342,1553.351879,1735.66103,1477.017641,1427.153349,1658.415794,1567.248106,1379.61936,1622.480869,1760.454178,1844.148397,1987.823725,1601.060629,1621.984243,2104.625702,1811.172485,2300.275803,2182.087183,1290.929079,1711.12895,1625.175476,2601.668358,1444.571018,1791.678667,2622.629642,1364.34412,1389.329433,1584.573746,2551.171541,1684.458733,1896.23189,1450.076342,1658.398867,1601.534843,1876.4081,1507.074594,1571.3799,1908.69236,1688.627005,1538.504362,1818.245411,2124.26281,1649.035931,2470.080137,1253.349543,1893.724203,2063.975096,1723.701477,2514.378071,1799.88265,1889.433146,1719.619036,1709.676981,1563.638926,2600.88563,1968.774796,1951.742649,1641.564608,2712.839127,1293.610096,2459.568977]]))
    # print(_stats([[2064.588547,1758.965492,2131.829739,1527.096272,2120.051622,1560.325384,1456.926584,1517.159462,1475.424051,2764.845133,2733.530521,2594.369173,1480.316162,1385.01811,2576.247215,2562.696218,1424.845695,1525.052309,2149.390221,2296.668291,1946.08283,1971.311331,2082.695723,1793.273449,1693.598747,1622.240067,2048.337221,2092.436314,1537.54282,1810.401917,2307.596207,1723.211288,2035.865784,1924.333572,1347.595453,1552.719116,2041.476488,1997.274399,1846.041679,1752.067327,1698.126316,2313.736677,1501.159191,1948.055983,1498.739719,1470.892191,1927.26469,2091.883898,1519.233704,2816.573143,1574.423552,1866.723537,1991.755486,1219.468832,1967.281818,1804.565191,1418.776989,2890.803814,2357.239008,2216.609001,1601.840973,1611.338139,2242.581367,1744.401693,2458.447933,2206.276417,1962.301731,1499.201298,1837.735415,1554.584742,1795.542955,2126.766205,1510.785341,1622.922659,1551.468849,2203.185081,1681.009054,1785.263777,3298.967838,3285.314322,1990.791082,1970.423222,1849.379063,1950.037241,2010.070801,1723.218203,2071.472168,2012.366056,1430.675507,2038.201094,1465.327501,1531.007528,1214.898586,1645.077467,1652.292013,1888.606071,2674.57962,2374.869108,1636.388779,1796.439171,1815.809727,2400.576353,1598.039627,1655.537605,1433.730125,1907.768965,1679.372072,1895.792961,1845.348597,1560.15873,1897.950649,1818.87579,2177.442074,1558.347702,2527.301788,2000.701904,1448.418617,1504.182339,1951.515913,2229.909658,1655.495167,1588.334322,1663.566113,1438.305378,1565.26804,1780.261755,1414.343119,1755.473852,1912.406683,2016.023874,1637.724876,1642.220736,2060.588598,1835.495472,1591.160059,1724.179029,2300.997496,1814.511776,2146.219492,1824.517012,1860.583782,1678.027391,1597.059488,1662.648916,2220.790625,2268.32366,2527.826309,1661.221504,2060.340643,1365.830421,2238.517761,2688.862085,1748.905659,1754.117727,1642.564774,2442.63196,1610.185862,1870.708466,1952.291727,1831.763029,1821.436405,1843.347073,2240.268707,1985.852003,2063.689947,1901.771545,1539.438248,1744.699001,1540.721416,1810.483456,1540.857792,2128.684044,1907.198668,1749.861479,2767.177343,2662.779808,2197.890759,1366.067648,2175.138474,1551.831245,2898.601055,1483.26087,1557.982206,1247.533798,1929.147959,1859.60412,1631.146193,1631.012678,1426.029682,1665.481806,1573.918104,1923.585653,1970.79277,1273.071289,1491.563082,1843.030214,1710.521221,2094.182014,1741.438866,2164.251089,2210.514545,1811.088324,1630.006075,1706.734896,2268.074036,1744.620323,1500.756979,1871.106625,2516.421556,1142.836094,1319.049835,1680.846214,2431.479931,2075.183868,1459.085941,2019.218206,1865.901232,1965.222597,1570.281744,1743.504047,1849.624157,1850.256205,1899.908304,2800.234079,1740.935802,1618.855,1619.540453,2364.036083,1721.017122,1441.200256,1411.062956,1990.271091,2696.802855,2198.002815,1795.130491,1504.183769,1777.957439,2186.91349,2206.400871,1641.463757,1799.30687,2610.152721,1784.627438,2092.383146,2200.196743,1429.179192,1583.832502,1461.960554]]))
    # print(_stats([[1716.466665,1884.124994,1999.495506,1979.032516,1609.925985,1560.157537,2679.470539,2504.470825,1711.073637,1396.252632,1597.10598,1919.901371,1648.405075,2187.635899,1886.393547,2481.970787,1504.946232,1693.537235,1698.220253,2028.629303,2075.647116,1787.592411,2104.985952,2034.738779,1393.149376,1954.551935,2116.094351,2376.843929,2105.57127,1614.320755,1465.71064,2147.725105,2192.393303,2294.264078,1533.46324,1703.047514,2687.946796,1893.518209,1442.969799,2002.368927,2073.954105,2325.85597,1372.087955,1737.633467,1510.249853,1781.494856,1884.84931,1559.290886,1657.433271,1701.526642,1575.100183,1470.359564,1281.416893,1522.818089,1883.261919,1706.278563,2557.986259,1501.035213,1826.296091,2056.543112,1483.239889,2053.902864,1498.936653,2272.098303,1953.754187,1450.85907,1723.820686,1582.457304,2018.92519,1702.759027,1607.360125,2165.441036,2097.226143,2406.592131,1686.181545,2331.119299,1648.655415,2176.262856,1621.012211,2198.749781,2483.82926,1665.331364,1589.566946,1889.31036,1635.461569,1914.002419,1779.043674,1983.561754,1628.804445,1765.273333,1507.949352,1893.872023,2249.254465,1699.979067,1633.093596,1630.515575,1645.311117,1366.074324,2636.778116,2151.277065,2056.019783,1308.632135,2794.984579,1757.820606,1919.187784,1855.137825,1876.458168,1521.346331,1488.21497,2031.4188,2465.242386,1638.533831,1931.16951,1373.957396,1603.403807,2814.246416,1638.114452,1789.427042,1750.432968,1640.133619,1916.095018,1840.728045,1662.486315,1669.747591,1553.198814,2215.882063,1618.075848,1480.942965,1412.328959,2436.621189,1305.012703,1961.752415,1557.31988,2663.952351,1696.56539,1434.109211,1545.851946,1854.939222,1573.913813,1801.100731,1923.355103,1586.752176,1688.318014,2364.905119,2444.490671,1507.131338,1519.933701,2237.284899,2359.805584,1759.066105,2029.138327,2255.057812,1722.881556,1209.517956,1939.489365,1473.517179,1864.86721,2510.591984,1676.730633,1570.374489,1968.434572,1366.436958,1931.52833,2139.605522,2266.615868,1912.773371,2155.452013,1973.233938,2528.022766,1996.357203,2125.962973,1775.918722,1606.901884,2307.785511,2062.437057]]))
    # print(_stats([[1536.617279,2338.461876,1663.403034,1876.796246,2833.355665,2776.845217,2074.419975,1694.05961,1426.983118,1633.024693,1718.84656,1738.181114,2052.974701,1714.585304,1968.601465,1568.014622,2586.582661,1843.149424,1599.939823,1662.945271,2519.436359,2421.576023,1734.38406,1573.544741,1669.591427,2538.668633,1934.550285,2129.998446,2177.343369,1572.815418,2317.657709,1991.544724,2036.587238,1895.426273,1650.069237,1708.098888,1563.170195,1546.559572,1937.766075,2065.935135,2730.845451,1753.930807,1767.080784,1943.123579,2293.045044,2030.214548,1529.585123,2463.85026,2067.776918,2191.805124,1839.630365,2378.210545,1679.393768,1497.829199,1480.801582,2045.182228,1985.11672,2211.712837,1605.166435,1418.878794,1986.304283,2254.221916,1779.549122,1737.852812,1435.704947,1646.249771,2131.655693,1904.814005,2179.645777,1912.644148,1351.512194,2453.131437,1602.700233,2064.229488,1591.099977,1492.071867,1590.070009,2031.415939,1671.357155,1972.344637,2126.554966,1926.042557,1983.537912,1922.35589,2071.434498,1958.207846,1469.226122,1413.698673,2132.588387,1527.661562,1462.115526,1766.130209,1866.103172,2701.976776,1866.662979,2245.817184,1829.684734,1640.295267,2070.645332,2149.666071,2410.598755,1952.330351,2012.569189,1655.523539,1597.485781,1643.774986,1928.471565,1690.734148,1709.404707,2894.021273,1652.299404,1779.392719,2155.916691,2394.236088,2174.772739,1825.097561,1780.282021,2379.77314,1637.145042,1605.974674,]]))
    # print(_stats([[1846.075296,2138.003588,1974.719048,1700.98114,1362.104177,1613.354921,1419.56234,1608.959198,2491.193056,2813.238382,2625.761509,2161.244869,2244.244814,1727.201462,1521.766186,1543.645382,1893.654585,1748.536825,1668.578386,1890.639305,1588.142395,2280.867338,2091.228724,2238.510609,1392.688274,1615.595579,1882.718086,2366.602421,1640.043259,1692.408323,2093.990564,1671.708107,1848.711252,1653.591633,2034.454584,1742.724895,2151.835442,2259.893179,2352.796078,2068.513155,1897.52984,1732.954025,1600.638866,1654.622793,1729.648113,2164.555073,2026.507854,1682.950497,1476.039171,2961.177826,1354.230165,1441.499949,1619.461298,1582.59964,]]))

    print(_stats([[1527.762175,2300.38476,2359.619856,2042.007685,1331.486464,1824.83077,2690.110207,1769.646645,1606.84967,3043.405294,1470.531702,1803.71809,2017.671108,2213.505507,1823.945761,1957.531929,1847.118378,1601.728439,2656.747818,1541.897297,1498.567104,1417.308331,1791.859627,1582.98707,1412.70709,1533.488512,1809.652805,1599.802494,1486.442327,1733.119726,1697.793961,2338.341951,2621.647358,1844.138622,1305.595875,1889.604092,1256.308556,2649.554253,2700.772524,2473.072529,2554.755211,1674.767256,1696.090937,1519.173861,1730.377197,1550.854445,1628.983259,1888.010025,1791.409731,1625.708818,1489.253759,1535.970211,2148.000717,1229.239941,1815.942526,1739.677906,1777.518272,2186.224461,1980.173349,1248.531103,1756.77824,1931.539536,2625.445127,1779.258251,1398.228884,1698.208094,1512.836218,1842.249632,1791.769028,1859.542847,1473.42515,1625.964403,2829.791546,1918.311834,1597.408056,1627.587557,2446.77949,2587.965012,1374.156475,2292.758465,1265.309811,1414.181709,2348.278046,2227.088451,1689.613581,1607.437134,1507.07221,1785.009146,1624.02153,1935.767174,1632.897854,1898.429155,1777.308941,1729.50387,1960.911751,1616.198063,1388.509035,1706.911564,2041.267157,2348.693132,1981.109381,1685.1542,1521.169901,1296.329975,2250.781298,2026.793957,1525.498629,1952.987909,2262.034178,1595.211506,1636.012793,1464.688301,2313.254595,1652.605534,2204.209328,1934.163094,1992.441416,2152.089596,1454.974174,1427.00839,2126.518011,1355.553389,1973.800659,1664.658308,1884.160042,1902.778149,2102.398396,1541.987419,1822.235107,1619.920492,1354.056597,1289.290428,1905.41029,1852.078199,1860.215425,2089.070797,1859.81226,1811.434031,2008.911848,1366.445541,1486.041546,1944.706678,1440.857172,2047.618151,1672.255039,1791.197062,2344.831944,1972.76473,1585.481167,1589.710236,1627.202749,1521.257877,1244.655132,1601.503372,1601.522923,1785.933495,2021.164894,1684.815407,1420.186281,1552.872419,2008.369207,2264.6873,1827.253103,1603.00231,1502.709627,1416.867733,2505.620718,1487.111807,1775.965214,2180.658579,2256.594181,2009.333611,1738.526344,2432.93643,2299.738884,2653.564692,1504.443645,1986.748695,1728.597403,1913.191795,1946.184635,1556.936026,1568.495989,1407.785416,1486.745596,1552.389383,1497.014284,1387.397289,2001.321077,1420.582533,1709.817648,1480.105877,1414.620638,1348.989487,1570.319176,1970.999241,1801.325321,1687.549591,1736.346722,1745.032072,1838.803291,1755.104303,1512.765169,1397.90678,1669.133186,1865.010977,1271.807909,1777.940989,2093.261003,1400.119781,1425.01235,1382.929087,1762.266397,1806.275129,1197.909117,1471.793413,1555.457592,2177.660227,1528.050184,1361.164331,1998.887062,1858.023405,1897.525787,1489.605188,1555.139065,1532.502413,2015.006781,1515.120745,1922.806501,1931.550026,1808.052301,1573.003292,1529.084206,1772.255659,1611.441135,1630.264044,1310.476303,1525.051832,1459.692955,1789.727211,1545.707703,1496.957541,1578.753471,1682.713509,1583.443165,2082.50761,1781.589985,1589.148521,1407.740355,2531.724453,1489.602566,1872.140408,1970.537901,1655.2279,1506.551266,1510.407925,2293.938875,1999.202013,1847.073793,2383.100748,1744.920731,1718.7953,1644.610882,1372.833967,1607.010126,1910.061598,2512.407303,1987.989187,2178.552628,1731.45628,1550.238609,1743.575811,2009.413481,2076.684713,1424.146891,1624.382734,1580.917358,1320.286751,1831.269979,2110.161543,1812.900066,1857.957125,1660.584927,1606.595755,1982.252359,1716.330767,2067.134619,1704.234362,1703.905582,1769.173861,1967.557192,2440.134287,1776.995897,1933.964252,1900.244236,1755.665779,1443.872929,2141.275644,2269.857883,2360.853672,1592.131138,1981.983662,2797.124863,1411.066532,1794.13867,1687.033415,1717.36455,1566.427708,1646.749258,1720.829248,1970.633984,1475.712061,2704.943657,1695.030928,2284.468889,1904.372215,1583.538532,1727.121592,1551.055431,2122.730494,1512.960196,1679.123163,1912.794352,1352.702618,1872.483492,2132.135391,1979.326963,1337.673426,1596.90094,1614.238262,2019.681215,1874.073505,2552.412033,2019.477844,1442.114592,1670.462132,2243.623734,1538.192987,1600.318432,1475.710869,1679.284811,2096.605062,1948.612213,1643.719912,2241.675377,2656.290054,2280.240297,1402.31204,1475.37446,1866.31465,1867.642403,1383.531809,1686.295986,1771.320581,1614.27021,1484.808683,1571.768761,1874.755383,1585.19721,1643.985033,1834.316254,2317.566633,2053.955793,2161.360025,2389.373541,1427.209616,1811.416388,1674.41082,2202.414989,1594.656706,1496.13142,1450.655937,1649.035215,1599.113226,2345.042467,1617.896557,1557.953835,2299.309969,3424.184799,1461.443901,1595.050573,1398.426294,1561.090946,1903.864145,1927.982807,1801.817656,1806.253672,1835.950613,2001.571178,1827.205896,1599.95389,1449.523211,1683.758736,1637.817383,1569.229841,1698.594332,2448.820114,1773.555279,2122.870207,1570.483685,1441.297531,1571.334362,1801.820755,1849.612474,1649.671793,1436.259747,1519.732237,1419.617414,1736.070156,1593.018532,1749.727488,2111.113071,1851.183176,2113.205433,1595.566034,1681.129456,2022.549152,1966.125965,1413.3811,1443.624973,1643.153429,2209.503651,2125.012875,1717.933893,1397.058487,1424.49379,1627.070665,2143.355846,1354.811907,1521.094799,1888.974428,1263.955355,1318.328857,1227.443457,2261.251211,2122.097731,1590.738058,1818.52293,1510.130644,1635.034323,1856.124878,1685.353041,1597.168446,2779.14691,2198.127508,2580.620527,1686.597586,1554.214001,1554.555893,2433.723927,2117.952585,1795.898914,2091.90011]]))