import requests

def search_duckduckgo(query):
    url = "https://api.duckduckgo.com/"
    params = {
        "q": query,
        "format": "json",
        "pretty": 1,
        "no_html": 1,
        "no_redirect": 1
    }

    response = requests.get(url, params=params)
    data = response.json()

    print("🔎 搜索词:", query)
    print("📘 标题:", data.get("Heading", "无"))
    print("📄 摘要:", data.get("Abstract", "无摘要"))
    print("🔗 链接:", data.get("AbstractURL", "无链接"))

    related = data.get("RelatedTopics", [])
    if related:
        print("\n📚 相关主题：")
        for item in related[:5]:  # 最多展示 5 条相关结果
            if isinstance(item, dict) and "Text" in item:
                print(f"- {item['Text']}: {item['FirstURL']}")

# 示例搜索
search_duckduckgo("Python 编程语言")
