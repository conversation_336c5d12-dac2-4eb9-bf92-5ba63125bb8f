import json
from collections import defaultdict

# 假设 data 是你的原始数据列表
data = [{"date":"2025-09-01","b":"2025-09-01T00:00:00","e":"2025-09-02T00:00:00","region":"中国大陆","userIpInfo":None,"guestIpInfo":None,"registerCount":48,"userCount":4408,"appUserCount":88,"appGuestCount":165,"h5UserCount":3,"h5GuestCount":30,"newGuestCount":144,"accessedGuestCount":199,"orderPageNewUserCount":22,"orderPageUserCount":39,"preCommitNewUserCount":12,"preCommitUserCount":21,"orderNewUserCount":5,"orderUserCount":12,"payNewUserCount":5,"firstPayUserCount":4,"notFirstPayUserCount":3,"c1":1,"c2":3,"payUserCount":12,"xhsIosCount":None,"xhsAndroidCount":None,"xhsH5Count":None,"appAvgDuration":None,"mealSetClickCount":25,"mealSetClickUserCount":12,"restDetailLoadCount":306,"restDetailLoadUserCount":100,"mapBookClickCount":1,"mapBookClickUserCount":1,"mapListBookClickCount":0,"mapListBookClickUserCount":0,"mapSearchCallCount":34,"mapSearchCallUserCount":9,"feedLoadCount":130,"feedLoadUserCount":99,"feedRestCount":131,"feedStrategyCount":0,"feedMealSetCount":13,"feedStrategyRestCount":0,"clickLoginWin":128,"closeLoginWin":169,"registerFromLoginWin":16},{"date":"2025-09-01","b":"2025-09-01T00:00:00","e":"2025-09-02T00:00:00","region":"港澳台","userIpInfo":None,"guestIpInfo":None,"registerCount":1,"userCount":275,"appUserCount":3,"appGuestCount":25,"h5UserCount":0,"h5GuestCount":0,"newGuestCount":21,"accessedGuestCount":25,"orderPageNewUserCount":0,"orderPageUserCount":2,"preCommitNewUserCount":0,"preCommitUserCount":1,"orderNewUserCount":0,"orderUserCount":1,"payNewUserCount":0,"firstPayUserCount":0,"notFirstPayUserCount":1,"c1":0,"c2":0,"payUserCount":1,"xhsIosCount":None,"xhsAndroidCount":None,"xhsH5Count":None,"appAvgDuration":None,"mealSetClickCount":0,"mealSetClickUserCount":0,"restDetailLoadCount":9,"restDetailLoadUserCount":4,"mapBookClickCount":0,"mapBookClickUserCount":0,"mapListBookClickCount":0,"mapListBookClickUserCount":0,"mapSearchCallCount":39,"mapSearchCallUserCount":1,"feedLoadCount":10,"feedLoadUserCount":9,"feedRestCount":7,"feedStrategyCount":0,"feedMealSetCount":0,"feedStrategyRestCount":0,"clickLoginWin":15,"closeLoginWin":23,"registerFromLoginWin":0},{"date":"2025-09-01","b":"2025-09-01T00:00:00","e":"2025-09-02T00:00:00","region":"国外","userIpInfo":None,"guestIpInfo":None,"registerCount":20,"userCount":924,"appUserCount":21,"appGuestCount":64,"h5UserCount":0,"h5GuestCount":4,"newGuestCount":45,"accessedGuestCount":68,"orderPageNewUserCount":7,"orderPageUserCount":10,"preCommitNewUserCount":3,"preCommitUserCount":5,"orderNewUserCount":2,"orderUserCount":5,"payNewUserCount":1,"firstPayUserCount":1,"notFirstPayUserCount":2,"c1":0,"c2":1,"payUserCount":4,"xhsIosCount":None,"xhsAndroidCount":None,"xhsH5Count":None,"appAvgDuration":None,"mealSetClickCount":5,"mealSetClickUserCount":3,"restDetailLoadCount":76,"restDetailLoadUserCount":31,"mapBookClickCount":0,"mapBookClickUserCount":0,"mapListBookClickCount":0,"mapListBookClickUserCount":0,"mapSearchCallCount":55,"mapSearchCallUserCount":4,"feedLoadCount":31,"feedLoadUserCount":27,"feedRestCount":25,"feedStrategyCount":0,"feedMealSetCount":4,"feedStrategyRestCount":0,"clickLoginWin":11,"closeLoginWin":58,"registerFromLoginWin":10}]

# 聚合字段
target_fields = [
    "feedLoadCount",
    "feedLoadUserCount",
    "feedRestCount",
    "feedStrategyCount",
    "feedMealSetCount",
    "feedStrategyRestCount",
    "clickLoginWin",
    "closeLoginWin",
    "registerFromLoginWin",
    "appUserCount",
    "appGuestCount",
    "h5UserCount",
    "h5GuestCount"
]

# 按 date 聚合
aggregated = defaultdict(lambda: defaultdict(int))

for entry in data:
    date = entry["date"]
    for field in target_fields:
        aggregated[date][field] += entry.get(field, 0) or 0

# 表头
header = "{:<12} {:>10} {:>8} {:>8} {:>8} {:>8} {:>10} {:>10} {:>8} {:>10}".format(
    "date",
    "userRatio",
    "load",
    "uLoad",
    "rest",
    "strategy",
    "mealSet",
    "sRest",
    "regLog",
    "closeRate"
)
print(header)
print("-" * len(header))

# 输出数据
for date, fields in sorted(aggregated.items()):
    feedLoadCount = fields["feedLoadCount"]
    feedLoadUserCount = fields["feedLoadUserCount"]
    feedRestCount = fields["feedRestCount"]
    feedStrategyCount = fields["feedStrategyCount"]
    feedMealSetCount = fields["feedMealSetCount"]
    feedStrategyRestCount = fields["feedStrategyRestCount"]
    registerFromLoginWin = fields["registerFromLoginWin"]
    clickLoginWin = fields["clickLoginWin"]
    closeLoginWin = fields["closeLoginWin"]
    totalUsers = fields["appUserCount"] + fields["appGuestCount"] + fields["h5UserCount"] + fields["h5GuestCount"]
    loginTotal = clickLoginWin + closeLoginWin

    feedLoadUserRatio = f"{(feedLoadUserCount / totalUsers * 100):6.2f}%" if totalUsers > 0 else "  0.00%"
    closeLoginRatio = f"{(closeLoginWin / loginTotal * 100):6.2f}%" if loginTotal > 0 else "  0.00%"

    # print("{:<12} {:>10} {:>8} {:>8} {:>8} {:>8} {:>10} {:>10} {:>8} {:>10}".format(
    #     date,
    #     feedLoadUserRatio,  # 已是字符串，如 '12.34%'
    #     feedLoadCount,
    #     feedLoadUserCount,
    #     feedRestCount,
    #     feedStrategyCount,
    #     feedMealSetCount,
    #     feedStrategyRestCount,
    #     registerFromLoginWin,
    #     closeLoginRatio  # 已是字符串，如 '70.00%'
    # ))

    print(f"{date},{feedLoadUserRatio},{feedLoadCount},{feedLoadUserCount},{feedRestCount},{feedStrategyCount},{feedMealSetCount},{feedStrategyRestCount},{registerFromLoginWin},{closeLoginRatio}")
