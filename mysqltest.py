import mysql.connector

def fetchAllNPrint():
    for row in cursor.fetchall():
        print(row)

# 创建连接
connection = mysql.connector.connect(
    host="localhost",
    user="root",
    password="Hekz1998",
    database="t240103"
)

# 创建游标
cursor = connection.cursor()

cursor.execute("SELECT * FROM table250113")
fetchAllNPrint()

cursor.execute("insert into table250113 (str_v, create_time) values ('b', '2025-01-13 01:00:00')")
fetchAllNPrint()
#
cursor.execute("SELECT * FROM table250113")
fetchAllNPrint()

cursor.execute("commit")

# 关闭游标和连接
cursor.close()
connection.close()