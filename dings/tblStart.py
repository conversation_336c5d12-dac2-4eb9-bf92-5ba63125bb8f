import requests
import json


def test_tbl_start():
    url = "https://dings.133.cn/online/order_manage4j/tblOrder/start" #正式
    url = "https://dingstest.133.cn/test/order_manage4j/tblOrder/start" #测试
    data = {
        "orderId": "RESORD216873772226",#订单号, 必填
        "tblBookParam": {
            "callTblBook": 0,#0为不调预定接口 , 可不填
            "specificEmail": "<EMAIL>"# 指定邮箱, 可不填
        }
    }
    headers = {
        "Content-Type": "application/json"
    }
    response = requests.post(url, data=json.dumps(data), headers=headers)
    print(response.text)

    # 响应示例:
    simple = {
        "tblBookReq": { # callTblBook为0时, 会返回预定接口请求
            "trace_id": "dc2f992e-d518-4bcd-bdd2-4a18b3bb0def",
            "visit_time": "20:00",
            "last_name": "<PERSON>uan",
            "baby_member": 0,
            "first_name_jp": "りょう",
            "rcd": "13290197",
            "phone": "08053826002",
            "member": 2,
            "notifyUrl": "https://dingstest.133.cn/test/order_manage4j/tblOrder/bookResNotify",
            "is_test": True,
            "last_name_jp": "かん",
            "first_name": "liang",
            "order_id": "test:RESORD216873772226:30",
            "plan_id": "242597978",
            "visit_date": "20250910",
            "email": "<EMAIL>"
        }
    }


if __name__ == "__main__":
    test_tbl_start()