import requests
import time
import os, sys
import re
import uuid
import random
import json
import pandas as pd
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm  # 用于显示进度条
from loguru import logger

from utils import read_csv, get_work_flow

project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_dir)

# 获取天猫非流式响应
def get_tmall_response_stream(sessionId, message):
    # 定义接口的基本U
    base_url = "http://10.0.51.213:14414?cmd=aichat"

    # 将参数编码为URL格式z
    data = {
        "domain":"tmall",
        "msg": message,
        "sessionId": sessionId,
        # "phoneid":"270127041",
        # "test":"DeepSeekV3",
        "stream": "true",
        # "onlyResult":"true",
    }

    proxies = {
        "http": "http://120.133.0.173:4780",
        "https": "http://120.133.0.173:4780"
    }
    response = requests.post(base_url, data=data, proxies=proxies, stream=True)
    print(response.text)
    response_text = ""
    for line in response.iter_lines(decode_unicode=True):
        if line:
            # logger.info(f"{line}")
            response_text = line
            # print(response_text)
    answer = response_text
    knowledge = ""

    return answer, knowledge


# 获取天猫网关测试
def get_tmall_response_stream_gw(sessionId, message):
    traceid = str(uuid.uuid4())
    CHAT_URL = "https://t.rsscc.com/pcg/gateway/agi/gpt/gouwuche"
    DEV_URL = "http://10.0.51.213:14414?cmd=dev"

    HEADERS = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9",
        "authorization": "Basic ODM2QUUxRURDRTZGQjI4QkU4QjE0MTU1NjdGNTBDQkE=",
        "origin": "https://wtest.133.cn",
        "priority": "u=1, i",
        "referer": "https://wtest.133.cn/",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "cross-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
    }
    params = {
        "hlid": "hlJ1NHKt",
        "hlsecret": "O5ryOG21",
        "projectver": "1.0.0",
        "modulecver": "8.7.7",
        "uid": "H5mHHyK3FKyD4tbK5SPukOWH",
        "uuid": "H5mHHyK3FKyD4tbK5SPukOWH",
        "systemtime": "1753777247200",
        "hlp": '{"operasys":"windows","sysver":"10","devicetype":"web","device":"unknown unknown","root":"0","linkmode":"wifi","screen":"1463,867,1.4"}',
        "tmc": "",
        "userChannel": "",
        "pid": "250999",
        "cmd": "aichat",
        "sessionId": sessionId,
        "msg": message,
        "citycode": "",
        "customer_question": "",
        "audio_stream": "false",
        "dataStream": "1",
        "domain": "tmall",
        "gtla": "undefined",
        "gtlo": "undefined",
        "fromsource": "",
        "sid": "02A37654",
        "traceid": traceid,
        # "onlineSearch": "0",
    }

    # 1) 客户端首 token
    start = time.time()
    client_ms = None
    answer = ""
    knowledge = ""
    try:
        with requests.post(CHAT_URL, params=params, headers=HEADERS, stream=True, timeout=15) as r:
            r.raise_for_status()
            content = ""
            for chunk in r.iter_lines(decode_unicode=True):
                if chunk and client_ms is None:
                    client_ms = (time.time() - start) * 1000
                print("abcdefg" + str(time.time()))
                print("abcdefg" + chunk)
                if chunk.startswith("data:"):
                    chunk_dict = json.loads(chunk[5:])
                    data_str = chunk_dict.get("data", {})
                    data_dict = json.loads(data_str) if isinstance(data_str, str) and data_str else {}
                    text = data_dict.get("text", "")
                    # print(text)
                    content += text
            # print(content)
            answer = content
    except Exception as e:
        print(f"[{traceid}] chat error: {e}")
        return {
            "answer": answer,
            "knowledge": knowledge,
            "traceid": traceid,
            "client_ms": client_ms,
        }
    def _iso_to_dt(s: str) -> datetime | None:
        try:
            return datetime.fromisoformat(s) if s else None
        except ValueError:
            return None
    def _ms(delta: timedelta | None):
        return round(delta.total_seconds() * 1000, 3) if delta else None

    # # 2) dev timeNodes
    # server_ms = param_ms = kb_ms = summary_ms = online_ms = None  # --- 新增 online_ms
    # try:
    #     dev = requests.post(DEV_URL, data={"traceid": traceid}, timeout=10)
    #     dev.raise_for_status()
    #     tn = dev.json().get("timeNodes", {})
    #     if tn:
    #         t_begin = _iso_to_dt(tn.get("begin"))
    #         t_stream = _iso_to_dt(tn.get("streamFirstMsg"))

    #         t_param_b = _iso_to_dt(tn.get("ModelFlowServiceCallStart"))
    #         t_param_e = _iso_to_dt(tn.get("ModelFlowServiceCallFinished"))

    #         t_kb_b = _iso_to_dt(tn.get("chain_TmallDocSearchChainService_b"))
    #         t_kb_e = _iso_to_dt(tn.get("chain_TmallDocSearchChainService_e"))

    #         t_online_b = _iso_to_dt(tn.get("waitOnlineSearchB"))      # --- 新增
    #         t_online_e = _iso_to_dt(tn.get("waitOnlineSearchE"))      # --- 新增

    #         server_ms = _ms(t_stream - t_begin) if t_stream and t_begin else None
    #         param_ms = _ms(t_param_e - t_param_b) if t_param_b and t_param_e else None
    #         kb_ms = _ms(t_kb_e - t_kb_b) if t_kb_b and t_kb_e else None
    #         summary_ms = _ms(t_stream - t_kb_e) if t_stream and t_kb_e else None
    #         online_ms = _ms(t_online_e - t_online_b) if t_online_b and t_online_e else None  # --- 新增
    # except Exception as e:
    #     print(f"[{traceid}] dev error: {e}")

    return {
        "answer": answer,
        "knowledge": knowledge,
        "traceid": traceid,
        "client_ms": client_ms,
        # "server_ms": server_ms,
        # "param_ms": param_ms,
        # "kb_ms": kb_ms,
        # "summary_ms": summary_ms,
        # "online_ms": online_ms        # --- 新增
    }




# 获取天猫流式响应
def get_tmall_response(sessionId, message):
    # 定义接口的基本U
    base_url = "http://10.0.51.213:14414?cmd=aichat"

    # 将参数编码为URL格式z
    data = {
        "domain":"tmall",
        "msg": message,
        "sessionId": sessionId,
        # "phoneid":"270127041",
        # "test":"DeepSeekV3",
        "onlyResult":"true",
    }

    proxies = {
        "http": "http://120.133.0.173:4780",
        "https": "http://120.133.0.173:4780"
    }
    response = requests.post(base_url, data=data, proxies=proxies)
    logger.info(f"{response.json()}")
    userHistory = response.json().get("data", {}).get('userHistory', {})
    if isinstance(userHistory, dict):
        answer = userHistory.get('content', "")
        createGptQuery = userHistory.get('chainContext', {}).get('createGptQuery', "")
    elif isinstance(userHistory, list):
        answer = userHistory[-1].get('content', "")
        createGptQuery = ""
    else:
        answer = ""
        createGptQuery = ""
        logger.error("userHistory is not a dict or list")
    pattern = re.compile(r'<知识库搜索结果>(.*?)</知识库搜索结果>', re.DOTALL)
    matched_result = re.findall(pattern, createGptQuery)
    knowledge = "".join(matched_result) if matched_result else ""
    return answer, knowledge


def add_ask_question(qa_history_str):
    # 天猫通用agent测试问题生成 appid
    APP_ID = "94867f6ab127494faa12f37bfc675140"
    response = get_work_flow(qa_history_str, APP_ID)
    # print(response)
    question = response.get("question", "")
    return question


def merge_history(qa_history):
    qa_histort_str = ""
    for idx, qa in enumerate(qa_history):
        qa_histort_str += f"第{idx+1}轮、 用户: {qa['question']}\t客服: {qa['answer']}\n"
    return qa_histort_str

# 处理整个对话会话
def process_session(sessionId, questions, topics, row_topics, stream=False):
    session_results = []
    print(f"Processing session: {sessionId}: {questions}")
    for idx, question in enumerate(questions):
        if not stream:
            # 非流式测试
            answer, knowledge = get_tmall_response(sessionId, question)
            client_ms = 0
        else:
            # 流式测试
            # answer, knowledge = get_tmall_response_stream(sessionId, question)
            result = get_tmall_response_stream_gw(sessionId, question)
            answer, knowledge, client_ms = result['answer'], result['knowledge'], result['client_ms']
        answer = answer.replace('\n', '\\n')
        knowledge = knowledge.replace('\n', '\\n')
        topic = topics[idx] if len(topics) > idx else ""
        row_topic = row_topics[idx] if len(row_topics) > idx else ""
        data = {"sessionId": sessionId, "round": idx+1, "topic": topic, "row_topic": row_topic, "question": question, "answer": answer, "client_ms": client_ms, "knowledge": knowledge,}
        logger.info(data)

        session_results.append(data)
    return session_results


# 处理整个对话会话
def process_session_add_ask(sessionId, questions, topics, row_topics, stream=False):
    session_results = []
    idx = 0
    topic = topics[idx] if len(topics) > idx else ""
    row_topic = row_topics[idx] if len(row_topics) > idx else ""
    print(f"Processing session: {sessionId}: {questions}, topic: {topic}, row_topic: {row_topic}")

    for question in questions:
        if not stream:
            # 非流式测试
            answer, knowledge = get_tmall_response(sessionId, question)
        else:
            # 流式测试
            # answer, knowledge = get_tmall_response_stream(sessionId, question)
            result = get_tmall_response_stream_gw(sessionId, question)
            answer, knowledge, client_ms = result['answer'], result['knowledge'], result['client_ms']
        answer = answer.replace('\n', '\\n')
        knowledge = knowledge.replace('\n', '\\n')
        qa_dict = {"sessionId": sessionId, "round": 1, "topic": topic, "row_topic": row_topic, "question": question, "answer": answer, "client_ms": client_ms, "knowledge": knowledge}
        logger.info(qa_dict)
        session_results.append(qa_dict)
        # 随机多轮追问
        random_rounds_num = random.randint(2, 5)
        # random_rounds_num = 2
        qa_history = [qa_dict]
        for i in range(random_rounds_num):
            qa_history_str = merge_history(qa_history)
            add_question = add_ask_question(qa_history_str)
            if not stream:
                # 非流式测试
                add_answer, add_knowledge = get_tmall_response(sessionId, add_question)
            else:
                # 流式测试
                # add_answer, add_knowledge = get_tmall_response_stream(sessionId, add_question)
                result = get_tmall_response_stream_gw(sessionId, add_question)
                add_answer, add_knowledge, client_ms = result['answer'], result['knowledge'], result['client_ms']
            add_answer = add_answer.replace('\n', '\\n')
            add_knowledge = add_knowledge.replace('\n', '\\n')
            qa_dict = {"sessionId": sessionId, "round": i+2, "topic": topic, "row_topic": row_topic, "question": add_question, "answer": add_answer, "client_ms": client_ms, "knowledge": add_knowledge}
            qa_history.append(qa_dict)
            logger.info(qa_dict)
            session_results.append(qa_dict)

    return session_results

# 使用线程池并发执行多个会话
def run_concurrent_tests(questions_file_path, output_csv_path, date_str, max_workers=15, stream=False):
    start_time = time.time()
    success_count = 0

    # 读取测试问题分组数据
    data = read_csv(questions_file_path)
    data=data[["session_id","question", "topic", "row_topic"]].dropna()
    grouped = data.groupby('session_id')[['question', "topic", "row_topic"]].agg(list).reset_index()

    # 结果列表
    results = []

    # 计算总问题数用于进度条
    total_questions =len(data)

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 创建任务：每个session_id一个任务
        futures = {}
        for _, row in grouped.iterrows():
            session_id = str(row['session_id']) + "_" + date_str
            questions = row['question']
            topics = row['topic']
            row_topics = row['row_topic']
            # future = executor.submit(process_session, session_id, questions, topics, row_topics, stream)
            future = executor.submit(process_session_add_ask, session_id, questions, topics, row_topics, stream)
            futures[future] = session_id

        # 添加 tqdm 进度条
        with tqdm(total=total_questions, desc="Processing", unit="question") as pbar:
            for future in as_completed(futures):
                session_results = future.result()
                results.extend(session_results)

                # 更新统计信息
                for result in session_results:
                    if result.get("answer") is not None and "请稍后再试" not in result.get("answer"):
                        success_count += 1

                # 更新进度条（每个问题更新一次）
                pbar.update(1)

    # 保存结果
    # 将结果保存到 Pandas DataFrame
    result_df = pd.DataFrame(results)
    # 保存为 CSV 文件
    result_df.to_csv(output_csv_path, index=False, encoding="utf-8-sig")

    end_time = time.time()
    total_time = end_time - start_time
    qps = success_count / total_time if total_time > 0 else 0

    print(f"总耗时: {total_time:.2f} 秒")
    print(f"QPS: {qps:.2f}")



# 从log中提取信息
def extract_info_from_log(log_file_path):
    sessionId_result_dict = {}
    with open(log_file_path, "r", encoding="utf-8") as file:
        for line in file:  # 逐行读取
            line_dict =eval(line)
            # print(type(line_dict))
            sessionId = line_dict.get("paraMap", {}).get("sessionId", "")
            question = line_dict.get("paraMap", {}).get("msg", "")
            answer = line_dict.get("data", {}).get('userHistory', {}).get('content', "")
            createGptQuery = line_dict.get("data", {}).get('userHistory', {}).get('chainContext', {}).get('createGptQuery', "")
            pattern = re.compile(r'<知识库搜索结果>(.*?)</知识库搜索结果>', re.DOTALL)
            matched_result = re.findall(pattern, createGptQuery)
            knowledge = "".join(matched_result) if matched_result else ""
            answer = answer.replace('\n', '\\n')
            knowledge = knowledge.replace('\n', '\\n')
            
            qa_dict = {"sessionId": sessionId, "question": question, "answer": answer, "knowledge": knowledge}
            sessionId_result_dict[sessionId] = [qa_dict] if sessionId not in sessionId_result_dict else sessionId_result_dict[sessionId] + [qa_dict]
    
    result_list = []
    for sessionId, qa_list in sessionId_result_dict.items():
        result_list += qa_list
    result_df = pd.DataFrame(result_list)
    result_df.to_csv(log_file_path.replace(".txt", "_result.csv"), index=False, encoding="utf-8-sig")
    print(f"已保存结果到 {log_file_path.replace('.txt', '_result.csv')}")


def extract_info_from_log2(log_file_path):
    sessionId_result_dict = {}
    with open(log_file_path, "r", encoding="utf-8") as file:
        for line in file:  # 逐行读取
            line_dict =eval(line)
            sessionId = line_dict.get("sessionId", "")
            sessionId_result_dict[sessionId] = [line_dict] if sessionId not in sessionId_result_dict else sessionId_result_dict[sessionId] + [line_dict]
    result_list = []
    for sessionId, qa_list in sessionId_result_dict.items():
        result_list += qa_list
    result_df = pd.DataFrame(result_list)
    result_df.to_csv(log_file_path.replace(".txt", "_result.csv"), index=False, encoding="utf-8-sig")
    print(f"已保存结果到 {log_file_path.replace('.txt', '_result.csv')}")

if __name__ == "__main__":
    print()
    # questions_file_path = os.path.join(project_dir, "data\天猫测试数据_20250723.csv")
    # questions_file_path = os.path.join(project_dir, "data\天猫测试数据_test.csv")

    # questions_file_path = os.path.join(project_dir, "data\天猫测试数据(新)_20250725.csv")
    questions_file_path = os.path.join(project_dir, "t250113\天猫测试数据(新)_20250725.csv")
    # questions_file_path = os.path.join(project_dir, "data/天猫测试结果多轮追问_20250801_094911.csv")
    # questions_file_path = os.path.join(project_dir, "data/天猫生成测试数据_20250801_161354_sampled.csv")
    
    # date_str = "20250718_150733-2"
    date_str = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    output_csv_path = os.path.join(project_dir, f"logs/天猫测试结果多轮追问_{date_str}.csv")
    
    # 并发数量 (每个session_id作为一个独立对话)
    max_workers = 5
    # stream = False
    stream = True
    run_concurrent_tests(questions_file_path, output_csv_path, date_str, max_workers, stream)
    
    # # 测试流式接口
    # sessionId = "20250723_150733-000001"
    # question = "老年旅客能否在登机口申请特殊餐食服务？"
    # result = get_tmall_response_stream_gw(sessionId, question)
    # print(result)

    # # 提取数据
    # txt_path = r"D:\work_hlth\my_project\hotel_agents\huawei_knowledge\logs\多轮追问测试20250801.txt"
    # extract_info_from_log2(txt_path)

    # qa_history_str = "第一轮：问：托运的行李里能带白酒吗？ 答：限制托运白酒，您要带多少白酒？"
    # add_question = add_ask_question(qa_history_str)
    # print(add_question)

