import datetime

import requests
import time
start=time.time()
# 定义接口的基本U
base_url = "http://***********:14414?cmd=aichat"
base_url = "http://**********:13313?cmd=aichat"
# base_url = "http://***********:13313?cmd=aichat"
base_url = "http://localhost:14414?cmd=aichat"


# 将参数编码为URL格式z
data = {
    "domain":"tripnow_train",
    "msg":"",
    # "sessionId": '2508181415',
    "sessionId": datetime.datetime.now().strftime("%Y%m%d%H%M%S"),
    "model":"DeepSeekV3",
    "onlyResult":"true",
    # "stream": "true",
    "phoneid": "26762163",
    # "test_mode": "有行程测试",
    # "traceid": "on9yjstiioz89u30"
}

proxies = {
    "http": "http://*************:4780",    "https": "http://*************:4780"
}
proxies = None

# if base_url.__contains__("localhost") or base_url.__contains__("192.168"):
#     response = requests.post(base_url, data=data)
# else:
#     response = requests.post(base_url, data=data, proxies=proxies)
# end=time.time()
# print(end-start)
# print(response.text)

arr = [r'登机陪伴助手', '赶不上飞机了怎么办呢']# 登机陪伴助手 赶不上飞机了怎么办呢 CA1509 何康正 北京到武汉车次推荐 北京到武汉车次推荐
for item in arr:
    data.update({"msg": item})
    response = requests.post(base_url, data=data, proxies=proxies)
    print(response.text)