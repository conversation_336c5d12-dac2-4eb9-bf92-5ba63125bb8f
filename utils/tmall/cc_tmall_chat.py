import requests
import time
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed

url = "https://t.rsscc.com/pcg/gateway/agi/gpt/gouwuche"

headers = {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "authorization": "Basic ODM2QUUxRURDRTZGQjI4QkU4QjE0MTU1NjdGNTBDQkE=",
    "origin": "https://wtest.133.cn",
    "priority": "u=1, i",
    "referer": "https://wtest.133.cn/",
    "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "cross-site",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
}

msg_list = [
    "国航宠物进机舱的尺寸限制是什么？",
    "南航宠物托运需要提前多久预约服务？",
    "海航对运输活体动物的包装箱有何特殊要求？",
    "宠物可以和主人一起登机吗？",
    "飞机上可以给宠物喂水吗？",
]  # 可根据需要扩展

def fetch_first_token_time(msg):
    params = {
        "hlid": "hlJ1NHKt",
        "hlsecret": "O5ryOG21",
        "projectver": "1.0.0",
        "modulecver": "8.7.7",
        "uid": "H5mHHyK3FKyD4tbK5SPukOWH",
        "uuid": "H5mHHyK3FKyD4tbK5SPukOWH",
        "systemtime": "1753777247200",
        "hlp": '{"operasys":"windows","sysver":"10","devicetype":"web","device":"unknown unknown","root":"0","linkmode":"wifi","screen":"1463,867,1.4"}',
        "tmc": "",
        "userChannel": "",
        "pid": "250999",
        "cmd": "aichat",
        "sessionId": "",
        "msg": msg,
        "citycode": "",
        "customer_question": "",
        "audio_stream": "false",
        "dataStream": "1",
        "domain": "tmall",
        "gtla": "undefined",
        "gtlo": "undefined",
        "fromsource": "",
        "sid": "02A37654"
    }

    start_time = time.time()
    first_token_time = None
    full_response = ""

    try:
        with requests.post(url, params=params, headers=headers, stream=True, timeout=10) as response:
            response.raise_for_status()
            for chunk in response.iter_content(chunk_size=1, decode_unicode=True):
                if chunk:
                    if first_token_time is None:
                        first_token_time = time.time()
                        elapsed_ms = (first_token_time - start_time) * 1000
                    full_response += chunk
        print(full_response)
    except Exception as e:
        print(f"请求失败：{e}")
        return None

    return elapsed_ms if first_token_time else None

# 线程数设置（并发数）
thread_count = 1

# 开始并发执行
print(f"\n并发请求中（{thread_count} 个线程）...\n")
results = []
with ThreadPoolExecutor(max_workers=thread_count) as executor:
    futures = [executor.submit(fetch_first_token_time, msg) for msg in msg_list]
    for future in as_completed(futures):
        result = future.result()
        if result is not None:
            results.append(result)

# 输出统计
print("\n\n首 token 统计结果（单位：ms）")
if results:
    for p in range(10, 100, 10):
        print(f"{p} 分位: {np.percentile(results, p):.2f} ms")
    print(f"平均值: {np.mean(results):.2f} ms")
else:
    print("无可用数据")