import datetime
import requests

base_url = "http://***********:14414?cmd=aichat"
# base_url = "http://**********:13313?cmd=aichat"
base_url = "http://localhost:14414?cmd=aichat"
# base_url = "http://***********:13313?cmd=aichat"

headers = {
    "Content-Type": "application/json"
}

proxies = {
    "http": "http://*************:4780",
    "https": "http://*************:4780"
}

# arr = ["国航宠物进机舱的尺寸限制是什么？","南航宠物托运需要提前多久预约服务？","海航对运输活体动物的包装箱有何特殊要求？","南航国际航班宠物托运的费用计算方式是怎样的？","各航空公司对宠物进机舱的尺寸限制是否有差异？具体标准是什么？","托运宠物的费用在不同航空公司之间是否有明显差异？如何查询具体价格？","携带宠物进入机舱是否需要提供疫苗接种证明？各航司要求是否一致？","国航允许怀孕多少周以内的孕妇无需提供医生证明即可正常购票？","深圳航空对孕晚期旅客（32周以上）申请特殊服务的具体流程是什么？","海南航空是否要求孕妇旅客必须由医护人员陪同乘机？","孕妇在飞机上突发状况时，航空公司有哪些应急措施？","携带胎心仪等医疗设备乘机需要提前办理哪些手续？","国航托运宠物需要提供哪些健康证明材料？","海航是否允许小型犬进入客舱？具体尺寸限制是什么？","各航空公司对孕晚期乘客的机上医疗服务和应急保障措施有何差异？","孕妇申请特殊客票退改签时，五家航空公司的手续费和限制条件是否相同？","国航、南航、东航等国内主要航空公司对孕妇乘机的政策是否存在差异？具体有哪些共同要求？","海南航空是否要求孕妇旅客提供特定医疗机构的健康证明？具体格式和内容需满足哪些条件？","中国东方航空对于怀孕超过32周的旅客，是否强制要求签署免责协议？具体内容包括哪些条款？","深圳航空是否为孕妇提供优先行李托运服务？办理该服务需要提前多少时间联系客服？","国内各大航空公司在孕妇旅客健康状况评估方面，通常要求提供哪些类型的医疗证明文件？"]
arr = ["CZ8886是什么情况"] # CZ8886是什么情况 飞机快吗

sessionId = datetime.datetime.now().strftime("%Y%m%d%H%M%S")

for idx, item in enumerate(arr, start=1):
    body = {
        "msg": item,
        "sessionId": sessionId,
        # "channel": "光帆",
        "stream": "true",
        "domain": "tmall",
        "dataStream": "1",
        "test": "true",
        # "phoneid": "270234783"
    }

    if "localhost" in base_url or "192.168" in base_url or "https" in base_url:
        response = requests.post(base_url, json=body, headers=headers, stream=True)
    else:
        response = requests.post(base_url, json=body, headers=headers, proxies=proxies, stream=True)

    print("\n" + "=" * 60)
    print(f"🟢 用户输入（第 {idx} 条）: {item}\n")
    print(f"🟡 系统回复（流式）：\n")

    for chunk in response.iter_content(chunk_size=None, decode_unicode=True):
        if chunk:
            print(chunk, end='', flush=True)

    print("=" * 60 + "\n")
