import os
import re
import json
import sys
from datetime import datetime
import numpy as np

# 时间格式适配
fmt1 = "%Y-%m-%dT%H:%M:%S"
fmt2 = "%Y-%m-%dT%H:%M:%S.%f"

def parse_time(s):
    try:
        return datetime.strptime(s, fmt1)
    except ValueError:
        return datetime.strptime(s, fmt2)

def parse_minute_time(s):
    return datetime.strptime(s, "%Y-%m-%dT%H:%M")

# 命令行参数解析
if len(sys.argv) < 2:
    print("用法: python msdifftmall.py <start_file> [end_file] [begin_time] [end_time]")
    sys.exit(1)

start_file = sys.argv[1]
end_file = sys.argv[2] if len(sys.argv) > 2 and not sys.argv[2].startswith("202") else None
begin_time_str = sys.argv[3] if len(sys.argv) > 3 else None
end_time_str = sys.argv[4] if len(sys.argv) > 4 else None

# 时间范围（精确到分钟）
begin_time = parse_minute_time(begin_time_str) if begin_time_str else None
end_time = parse_minute_time(end_time_str) if end_time_str else None

log_dir = "."
match_keyword = "ModelFlowServiceCallFinished"

# 文件列表准备
all_files = sorted([f for f in os.listdir(log_dir) if f.startswith("chat_")])
if "chat.log" in os.listdir(log_dir):
    all_files.append("chat.log")

if start_file not in all_files:
    print(f"[错误] start_file 不存在: {start_file}")
    sys.exit(1)

if end_file and end_file not in all_files:
    print(f"[错误] end_file 不存在: {end_file}")
    sys.exit(1)

start_index = all_files.index(start_file)
end_index = all_files.index(end_file) + 1 if end_file else len(all_files)
process_files = all_files[start_index:end_index]

# 累计变量
diff1_list, diff2_list, diff3_list = [], [], []
diff4_list, diff5_list = [], []
count = chain_count = chain_stream_count = 0

print("待处理文件列表:")
for f in process_files:
    print(f)

for file_name in process_files:
    file_path = os.path.join(log_dir, file_name)
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if match_keyword in line:
                    match = re.search(r'({.*?})', line)
                    if not match:
                        continue
                    try:
                        row = json.loads(match.group(1))

                        t_stream = parse_time(row["streamFirstMsg"])

                        # 动态时间范围限制
                        if begin_time and t_stream < begin_time:
                            continue
                        if end_time and t_stream > end_time:
                            continue

                        t_begin = parse_time(row["begin"])
                        t_call_start = parse_time(row["ModelFlowServiceCallStart"])
                        t_call_end = parse_time(row["ModelFlowServiceCallFinished"])

                        diff1 = (t_stream - t_begin).total_seconds() * 1000
                        diff2 = (t_call_end - t_call_start).total_seconds() * 1000
                        diff3 = (t_stream - t_call_end).total_seconds() * 1000

                        diff1_list.append(diff1)
                        diff2_list.append(diff2)
                        diff3_list.append(diff3)
                        count += 1

                        if "chain_TmallDocSearchChainService_b" in row and "chain_TmallDocSearchChainService_e" in row:
                            t_chain_b = parse_time(row["chain_TmallDocSearchChainService_b"])
                            t_chain_e = parse_time(row["chain_TmallDocSearchChainService_e"])
                            diff4 = (t_chain_e - t_chain_b).total_seconds() * 1000
                            diff4_list.append(diff4)
                            chain_count += 1

                            diff5 = (t_stream - t_chain_e).total_seconds() * 1000
                            diff5_list.append(diff5)
                            chain_stream_count += 1

                    except Exception as e:
                        print(f"[跳过记录] {file_name}: {e}")
    except Exception as e:
        print(f"[读取失败] {file_name}: {e}")

# 输出统计
def print_stats(name, data):
    print(f"{name} avg: {np.mean(data):.2f} ms")
    print(f"{name} 分位数 (p10 ~ p90): ", end='')
    print(', '.join([f"{p}p={np.percentile(data, p):.1f}" for p in range(10, 100, 10)]))

if count > 0:
    print(f"\n共处理 {count} 条记录")
    print_stats("1. 首token", diff1_list)
    print_stats("2. 工作流", diff2_list)
    # print_stats("3. 首token晚于结束", diff3_list)
    if chain_count > 0:
        print_stats(f"4. 知识库 (共{chain_count}条)", diff4_list)
    if chain_stream_count > 0:
        print_stats(f"5. 模型首token (共{chain_stream_count}条)", diff5_list)
else:
    print("无有效记录")