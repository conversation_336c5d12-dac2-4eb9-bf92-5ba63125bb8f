import json

import requests
url = "http://10.0.51.213:14414?cmd=dev"
data = {
        "traceid":"d41d69e4-1513-4fed-b267-49189cf54eef"
    }
proxies = {
    "http": "http://120.133.0.173:4780",
    "https": "http://120.133.0.173:4780"
}
response = requests.post(url, data=data)

print(response.text)

# 响应示例: {
#     "traceid": "d41d69e4-1513-4fed-b267-49189cf54eef",
#     "msg": "查询成功",
#     "code": "1001",
#     "success": true,
#     "timeNodes": {
#         "chain_TmallDocSearchChainService_b": "2025-07-30T15:48:08.448",
#         "chain_BlacklistFilterChainService_e": "2025-07-30T15:48:07.437",
#         "chain_ModelChainService_e": "2025-07-30T15:48:08.448",
#         "chain_ModelChainService_b": "2025-07-30T15:48:07.438",
#         "finished": "2025-07-30T15:48:10.671",
#         "ModelFlowServiceCallFinished": "2025-07-30T15:48:08.446",
#         "streamFirstMsg": "2025-07-30T15:48:09.404",
#         "ModelFlowServiceCallStart": "2025-07-30T15:48:07.439",
#         "chain_BlacklistFilterChainService_b": "2025-07-30T15:48:07.023",
#         "chain_OnlineSearchChainService_b": "2025-07-30T15:48:08.448",
#         "chain_TmallDocSearchChainService_e": "2025-07-30T15:48:08.91",
#         "begin": "2025-07-30T15:48:07.011",
#         "chain_OnlineSearchChainService_e": "2025-07-30T15:48:08.448"
#     }
# }