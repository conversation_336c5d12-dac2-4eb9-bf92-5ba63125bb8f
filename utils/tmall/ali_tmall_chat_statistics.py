import requests
import time
import numpy as np

url = "https://t.rsscc.com/pcg/gateway/agi/gpt/gouwuche"

headers = {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "authorization": "Basic ODM2QUUxRURDRTZGQjI4QkU4QjE0MTU1NjdGNTBDQkE=",
    "origin": "https://wtest.133.cn",
    "priority": "u=1, i",
    "referer": "https://wtest.133.cn/",
    "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "cross-site",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
}

msg_list = ["国航宠物进机舱的尺寸限制是什么？","南航宠物托运需要提前多久预约服务？","海航对运输活体动物的包装箱有何特殊要求？","南航国际航班宠物托运的费用计算方式是怎样的？","各航空公司对宠物进机舱的尺寸限制是否有差异？具体标准是什么？","托运宠物的费用在不同航空公司之间是否有明显差异？如何查询具体价格？","携带宠物进入机舱是否需要提供疫苗接种证明？各航司要求是否一致？","国航允许怀孕多少周以内的孕妇无需提供医生证明即可正常购票？","深圳航空对孕晚期旅客（32周以上）申请特殊服务的具体流程是什么？","海南航空是否要求孕妇旅客必须由医护人员陪同乘机？","孕妇在飞机上突发状况时，航空公司有哪些应急措施？","携带胎心仪等医疗设备乘机需要提前办理哪些手续？","国航托运宠物需要提供哪些健康证明材料？","海航是否允许小型犬进入客舱？具体尺寸限制是什么？","各航空公司对孕晚期乘客的机上医疗服务和应急保障措施有何差异？","孕妇申请特殊客票退改签时，五家航空公司的手续费和限制条件是否相同？","国航、南航、东航等国内主要航空公司对孕妇乘机的政策是否存在差异？具体有哪些共同要求？","海南航空是否要求孕妇旅客提供特定医疗机构的健康证明？具体格式和内容需满足哪些条件？","中国东方航空对于怀孕超过32周的旅客，是否强制要求签署免责协议？具体内容包括哪些条款？","深圳航空是否为孕妇提供优先行李托运服务？办理该服务需要提前多少时间联系客服？","国内各大航空公司在孕妇旅客健康状况评估方面，通常要求提供哪些类型的医疗证明文件？"]

first_token_times = []

for idx, msg in enumerate(msg_list):
    print(f"\n================ 第 {idx + 1} 条 =================\n问题：{msg}")

    params = {
        "hlid": "hlJ1NHKt",
        "hlsecret": "O5ryOG21",
        "projectver": "1.0.0",
        "modulecver": "8.7.7",
        "uid": "H5mHHyK3FKyD4tbK5SPukOWH",
        "uuid": "H5mHHyK3FKyD4tbK5SPukOWH",
        "systemtime": "1753777247200",
        "hlp": '{"operasys":"windows","sysver":"10","devicetype":"web","device":"unknown unknown","root":"0","linkmode":"wifi","screen":"1463,867,1.4"}',
        "tmc": "",
        "userChannel": "",
        "pid": "250999",
        "cmd": "aichat",
        "sessionId": "",
        "msg": msg,
        "citycode": "",
        "customer_question": "",
        "audio_stream": "false",
        "dataStream": "1",
        "domain": "tmall",
        "gtla": "undefined",
        "gtlo": "undefined",
        "fromsource": "",
        "sid": "02A37654"
    }

    start_time = time.time()
    first_token_time = None
    full_response = ""

    try:
        with requests.post(url, params=params, headers=headers, stream=True, timeout=10) as response:
            response.raise_for_status()

            for chunk in response.iter_content(chunk_size=1, decode_unicode=True):
                if chunk:
                    if first_token_time is None:
                        first_token_time = time.time()
                        elapsed_ms = (first_token_time - start_time) * 1000
                        first_token_times.append(elapsed_ms)
                        print(f"\n⏱ 首 token 耗时: {elapsed_ms:.2f} ms\n")
                    print(chunk, end="", flush=True)
                    full_response += chunk

    except Exception as e:
        print(f"\n❌ 请求失败：{e}")

# 统计分位数
print("\n\n📊 首 token 统计结果（单位：ms）")

if first_token_times:
    for p in range(10, 100, 10):
        print(f"{p} 分位: {np.percentile(first_token_times, p):.2f} ms")
    print(f"平均值: {np.mean(first_token_times):.2f} ms")
else:
    print("⚠️ 无可用数据")
