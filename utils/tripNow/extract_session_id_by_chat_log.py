import re

log_text = """
chat_2025063015_0.log:2025-06-30 15:35:41 [chat] - cost=16.978s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"","domain":"tmall"}, msg=宠物能带上高铁吗, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=52c8846c-3612-4d66-bd98-7d592893d55f} :101
chat_2025063015_0.log:2025-06-30 15:35:52 [chat] - cost=8.822s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=我的狗16kg可以吗, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=7b82abff-6d03-4c81-a86b-fa51dfa9a154} :101
chat_2025063015_0.log:2025-06-30 15:36:06 [chat] - cost=7.904s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=那怎么办, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=0786e200-bea7-4c58-a3f2-52365191d182} :101
chat_2025063015_0.log:2025-06-30 15:36:39 [chat] - cost=9.634s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=我的狗30斤重可以吗, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=b9af60f5-9d56-4618-97a6-3d8a558dd89d} :101
chat_2025063015_0.log:2025-06-30 15:37:09 [chat] - cost=11.974s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=可以带两只狗吗, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=76ca692a-3cd6-4be7-a49c-a54a9dd6fb27} :101
chat_2025063015_0.log:2025-06-30 15:48:12 [chat] - cost=10.041s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"火车票","domain":"tmall"}, msg=在售票窗口购买高铁票时，需要提供哪些有效证件？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=fb683d13-651f-4996-a93c-0f14ed93bc37} :101
chat_2025063015_0.log:2025-06-30 15:49:33 [chat] - cost=9.839s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=网购车票已经取票了，还能更改车次吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=889a3e51-0a0d-4b44-9d4a-29aa0a432fd6} :101
chat_2025063015_0.log:2025-06-30 15:52:47 [chat] - cost=9.209s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=车票丢了怎么办？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=c032457e-fa07-4f2e-93a0-4e54a449e60d} :101
chat_2025063015_0.log:2025-06-30 15:55:22 [chat] - cost=10.276s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=车票丢了，车子也开走了，还能退票吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=270e245f-d16d-4de0-ac3d-474c70b7517a} :101
chat_2025063015_0.log:2025-06-30 15:57:10 [chat] - cost=10.686s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"","domain":"tmall"}, msg=到了高铁站，发现自己没带身份证怎么办？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=5ce64934-fe24-4c32-b6e2-65b98b94e157} :101
chat_2025063016_0.log:2025-06-30 16:00:08 [chat] - cost=12.389s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"","domain":"tmall"}, msg=高铁站怎么办临时身份证？需要提供哪些信息？怎么证明自己身份？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=886696ec-8940-45c1-a8c0-b524ed0fac81} :101
chat_2025063016_0.log:2025-06-30 16:01:53 [chat] - cost=8.933s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"火车票","domain":"tmall"}, msg=网上退了高铁票后，大概多久退款能到账？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=53ecda24-542a-4c74-89d7-3ae309ace9f6} :101
chat_2025063016_0.log:2025-06-30 16:19:57 [chat] - cost=16.494s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"","domain":"tmall"}, msg=二代身份证过期了还能坐高铁吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=ab2b0946-ddef-402e-8c6b-13b357b97816} :101
chat_2025063016_0.log:2025-06-30 16:20:47 [chat] - cost=10.153s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=如何在铁路12306APP上申请临时电子身份证？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=07c47f89-9229-4617-ba0b-263d9c7e2a88} :101
chat_2025063016_0.log:2025-06-30 16:21:55 [chat] - cost=9.835s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"","domain":"tmall"}, msg=使用护照、港澳通行证等其他证件购买车票后如何进站？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=2cf4341a-22c1-40dd-b73d-d60ca804b92d} :101
chat_2025063016_0.log:2025-06-30 16:23:31 [chat] - cost=11.77s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=哪些物品不能带上高铁？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=575d8b40-a2a6-4a71-953f-8d4137a6fdb1} :101
chat_2025063016_0.log:2025-06-30 16:28:19 [chat] - cost=10.316s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=坐高铁如果携带了违禁物品但又不想丢弃，有什么办法处理？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=47f75a88-9d8b-4a17-b33d-3d5cf8763931} :101
chat_2025063016_0.log:2025-06-30 16:30:14 [chat] - cost=8.65s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"火车票","domain":"tmall"}, msg=高铁提前多久开始检票？开车前多久停止检票？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=baab2dea-8628-462e-bdfd-c6e78c226059} :101
chat_2025063016_0.log:2025-06-30 16:31:54 [chat] - cost=10.225s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"","domain":"tmall"}, msg=小孩子坐高铁要买票吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=243acf62-3805-4e01-8518-89542a884f7b} :101
chat_2025063016_0.log:2025-06-30 16:33:49 [chat] - cost=10.184s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"","domain":"tmall"}, msg=高铁的一等座和二等座有什么区别？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=722ae6bb-18a9-41d1-9a66-a0b2da19eea7} :101
chat_2025063016_0.log:2025-06-30 16:35:43 [chat] - cost=8.914s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"","domain":"tmall"}, msg=高铁上有插座吗？插座在哪里？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=312fc6c2-9021-4a80-b226-3d70944b0ba4} :101
chat_2025063016_0.log:2025-06-30 16:38:33 [chat] - cost=10.017s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"","domain":"tmall"}, msg=高铁上能否携带宠物，有哪些特殊规定？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=26acfbcb-0e8a-434a-8472-4193ea9c6723} :101
chat_2025063016_0.log:2025-06-30 16:47:00 [chat] - cost=13.549s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=目的地车票卖完了，可以买近站然后上车后补票吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=d598fe30-10dc-4d88-b78d-25ae432fde5a} :101
chat_2025063016_0.log:2025-06-30 16:52:18 [chat] - cost=8.791s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"","domain":"tmall"}, msg=防晒喷雾可以带上高铁吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=4ef7116e-add0-4b95-9378-e2db1d2612de} :101
chat_2025063016_0.log:2025-06-30 16:55:44 [chat] - cost=9.137s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"火车票","domain":"tmall"}, msg=赶火车没带身份证怎么办, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=b860ebca-954e-4a39-90fb-daf9c1d5d093} :101
chat_2025063017_0.log:2025-06-30 17:01:02 [chat] - cost=11.947s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=火车上可以点外卖么, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=df6af89b-4889-4897-ad2b-7dbc176736d0} :101
chat_2025063017_0.log:2025-06-30 17:06:38 [chat] - cost=12.022s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"火车票","domain":"tmall"}, msg=赶不上车能免费改签吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=8355f92f-126c-4a25-a6c9-870e7a37e8f8} :101
chat_2025063017_0.log:2025-06-30 17:11:36 [chat] - cost=9.319s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"火车票","domain":"tmall"}, msg=坐过站了咋办？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=2e25183d-8286-4e68-bcda-2a0b4a993e0f} :101
chat_2025063017_0.log:2025-06-30 17:17:34 [chat] - cost=8.514s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=虹桥机场离火车站多远, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=411dec6c-7d86-4bd9-913d-bfee1e5ebb82} :101
chat_2025063017_0.log:2025-06-30 17:19:59 [chat] - cost=11.601s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=春秋航空能带多少行李, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=63a04338-1bd3-4cb7-bf9a-a978d1bbb912} :101
chat_2025063017_0.log:2025-06-30 17:21:56 [chat] - cost=10.459s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=国际航班能带吃的上去么, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=f3aded2b-2cc5-4097-a569-9d20190a522b} :101
chat_2025063017_0.log:2025-06-30 17:23:26 [chat] - cost=9.164s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=婴儿车能上飞机吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=73186c37-af13-4899-a544-1b625a865db9} :101
chat_2025063017_0.log:2025-06-30 17:25:09 [chat] - cost=9.973s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=购买机票有哪些渠道，哪些渠道比较优惠？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=0c82f099-8cfe-4314-b3fe-b1fb8c81b418} :101
chat_2025063017_0.log:2025-06-30 17:26:32 [chat] - cost=8.762s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=充电宝可以带上飞机吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=9b96192d-a3d8-4ef1-8cca-fa94fcb6247f} :101
chat_2025063017_0.log:2025-06-30 17:28:38 [chat] - cost=9.7s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=充电宝能带多大容量的？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=1e188fbf-f781-4a48-8aa1-b57704d155ab} :101
chat_2025063017_0.log:2025-06-30 17:30:31 [chat] - cost=10.274s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=坐飞机可以托运多重的行李？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=ffd987d1-0b68-4e0d-b203-ae577bfe13e0} :101
chat_2025063017_0.log:2025-06-30 17:35:58 [chat] - cost=7.834s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=坐飞机选哪里的座位不容易晕机？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=e1625e62-b683-4f5b-986e-dd59a695797c} :101
chat_2025063017_0.log:2025-06-30 17:36:19 [chat] - cost=10.078s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=坐飞机可以携带多大尺寸的行李？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=c32c9eb2-2bac-40ba-993c-203ac15acc67} :101
chat_2025063017_0.log:2025-06-30 17:42:41 [chat] - cost=9.228s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=提前多久开始登机？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=af8e90ab-90f7-479f-b1f9-b2e2c0454092} :101
chat_2025063017_0.log:2025-06-30 17:46:00 [chat] - cost=9.523s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=登机口什么时候关闭？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=4d0f72ac-df9f-4a4d-af70-4d8f2b7ad6f0} :101
chat_2025063017_0.log:2025-06-30 17:48:55 [chat] - cost=7.746s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=错过飞机了可以改签航班吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=5288c149-eef8-4663-90e6-885db3652c38} :101
chat_2025063017_0.log:2025-06-30 17:51:52 [chat] - cost=9.615s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=第一次坐飞机，登机流程是什么？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=22bfcf1e-3d8c-46fe-9d94-731ef9fc75ca} :101
chat_2025063017_0.log:2025-06-30 17:54:16 [chat] - cost=10.101s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=怎么找到托运的行李转盘？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=87bb61c7-997b-4605-9a0d-312b47e25566} :101
chat_2025063017_0.log:2025-06-30 17:56:07 [chat] - cost=7.932s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=坐飞机必须开飞行模式吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=01fa5177-24f9-4fc2-bf8c-b14eae319a49} :101
chat_2025063017_0.log:2025-06-30 17:56:18 [chat] - cost=9.839s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=晕机怎么办？什么方式能缓解？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=d7906913-e14d-412e-9a0f-6d1162492928} :101
chat_2025063018_0.log:2025-06-30 18:13:19 [chat] - cost=15.951s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=买了飞机票之后怎么改签？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=23186579-561b-4d9e-a467-f17efedd2622} :101
chat_2025063018_0.log:2025-06-30 18:14:07 [chat] - cost=10.107s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=网上买的飞机票，怎么取票？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=9bf718f4-9623-47b1-9826-f0478dde38e8} :101
chat_2025063018_0.log:2025-06-30 18:15:29 [chat] - cost=13.554s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=坐飞机怎么中转？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=3975283d-474d-4fd1-989e-6c4a9181cbb2} :101
chat_2025063018_0.log:2025-06-30 18:16:03 [chat] - cost=8.212s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=充电宝可以带上飞机吗？, sessionId=9c523c60-d423-45b8-9cf7-53aca494203c, stream=true, traceid=f051369c-a5f1-4a26-89da-ecef4c5b840a} :101
chat_2025063018_0.log:2025-06-30 18:16:38 [chat] - cost=8.234s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"","domain":"tmall"}, msg=护照和签注有什么区别？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=0572d231-8f4f-413b-bcf9-fad711fecceb} :101
chat_2025063018_0.log:2025-06-30 18:17:52 [chat] - cost=9.846s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=坐飞机出国需要什么手续？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=d0991cfd-a7d0-4809-8513-2a6ebb86dc93} :101
chat_2025063018_0.log:2025-06-30 18:18:44 [chat] - cost=7.842s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=飞机可以托运护肤品吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=0a2e0a16-b44a-41c1-9b53-682285cf418b} :101
chat_2025063018_0.log:2025-06-30 18:19:43 [chat] - cost=10.062s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=五六岁的小孩子坐飞机需要买票吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=97f8d027-b0b3-4985-aba3-5e3e0e9fcc0c} :101
chat_2025063018_0.log:2025-06-30 18:20:44 [chat] - cost=8.956s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=航班延误了可以申请赔偿吗？, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=b8f132e7-4907-4460-a9d5-b02f39c10d3b} :101
chat_2025063018_0.log:2025-06-30 18:23:47 [chat] - cost=11.279s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=带两个小孩坐飞机，要怎么买票, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=ad3210fe-2f2f-4785-83c1-6629a24181bf} :101
chat_2025063018_0.log:2025-06-30 18:24:40 [chat] - cost=9.784s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","businessType":"机票","domain":"tmall"}, msg=两个都是儿童, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=e13223e9-8338-4d44-a1ea-73c0fdc9e448} :101
chat_2025063018_0.log:2025-06-30 18:25:16 [chat] - cost=9.006s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=一个八岁 一个三岁, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=53af9a0b-d7dc-4883-ba0d-7ab9c6ee9fa3} :101
chat_2025063018_0.log:2025-06-30 18:26:06 [chat] - cost=10.734s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=不占座和大人坐一起吗, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=e9283a44-ba98-4f4a-9d4a-8516c62d1d8d} :101
chat_2025063018_0.log:2025-06-30 18:26:55 [chat] - cost=11.725s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=那需要填写信息码, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=bffd6326-9037-4675-8473-34d054e0e812} :101
chat_2025063018_0.log:2025-06-30 18:27:31 [chat] - cost=9.514s | success=true:OK(1001) | para={cmd=aichat, domain=tmall, extendQuery={"application":"","domain":"tmall","businessType":""}, msg=不占座需要填写信息吗, sessionId=6868d438-8609-497f-904f-15de4eb4379c, stream=true, traceid=f010f9d8-7a15-4fad-87fe-54c4c25f6f6e} :101

"""

session_ids = set(re.findall(r'sessionId=([a-f0-9\-]+)', log_text))

for sid in sorted(session_ids):
    print(sid)


# import requests
# url = "http://10.0.51.213:14414?cmd=getsession"
# data = {
#         "sessionId":"6868d438-8609-497f-904f-15de4eb4379c",
#             "domain":"tmall",
#     }
# proxies = {
#     "http": "http://120.133.0.173:4780",
#     "https": "http://120.133.0.173:4780"
# }
# response = requests.post(url, data=data,proxies=proxies)
#
# print(response.text)