import requests
import json

base_url = "http://10.0.110.188:13333/dynamic"

data = {"dynamicNo":"CZ6801","departDate":"2025-07-16","org":"乌鲁木齐天山","dst":"喀什徕宁"}

headers = {
    "Content-Type": "application/json"
}

proxies = {
    "http": "http://120.133.0.173:4780",
    "https": "http://120.133.0.173:4780"
}

response = requests.post(base_url, json=data, headers=headers, proxies=proxies)
print(response.text)
