import datetime

import requests
import time
start=time.time()
# 定义接口的基本U
base_url = "http://***********:14414?cmd=addMsg"
# base_url = "http://**********:13313?cmd=aichat"
# base_url = "http://localhost:14414?cmd=addMsg"


# 将参数编码为URL格式z
data = {
    "domain":"tripnow2",
    "msg":"你好",
    "data": "{\"bizType\": \"test\",\"data\": {\"content\": \"dsfjlkfdsjakljfklds\"}}",
    "sessionId":"82ef6f17-720d-4d97-89b2-3f5b7473b250"
}

proxies = {
    "http": "http://*************:4780",    "https": "http://*************:4780"
}

arr = [r'你好']
for item in arr:
    data.update({"msg": item})
    if base_url.__contains__("localhost") or base_url.__contains__("192.168"):
        response = requests.post(base_url, data=data)
    else:
        response = requests.post(base_url, data=data, proxies=proxies)
    print(response.text)