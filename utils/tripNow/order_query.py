import requests
import time

TRIP_NOW_TEST__ = "TMFORDt261c2fd7c8dc4ef9bb67d67973fd7555tma11TripNowTest_13"
start=time.time()
base_url = "http://***********:14414?cmd=tmallAgentOrder"
# base_url = "http://**********:13313?cmd=tmallAgentOrder"
# base_url = "http://localhost:14414?cmd=tmallAgentOrder"

data = {
    "order_id":"lostNFound_fake_order"
}
proxies = {
    "http": "http://*************:4780",    "https": "http://*************:4780"
}

arr = [r'nonsense']
for item in arr:
    data.update({"msg": item})
    if base_url.__contains__("localhost") or base_url.__contains__("192.168"):
        response = requests.post(base_url, data=data)
    else:
        response = requests.post(base_url, data=data, proxies=proxies)
    print(response.text)