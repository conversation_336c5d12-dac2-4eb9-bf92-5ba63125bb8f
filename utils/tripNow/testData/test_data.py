import asyncio
import datetime
import platform
import uuid
import requests
import json
import os

# 指定要监控的航线（起点, 终点）
TARGET_ROUTES = [
    ("上海", "北京"),
    ("广州", "北京"),
    ("海口", "北京"),
    ("深圳", "北京"),
    ("济南", "青岛"),
    ("成都", "重庆"),
    ("厦门", "福州"),
    ("烟台", "济南"),
    ("乌鲁木齐", "喀什"),
    ("桂林", "广州"),
    ("重庆", "成都"),
    ("天津", "北京"),
    ("南昌", "广州"),
    ("漳州", "福州"),
    ("昆明", "丽江"),
    ("北京", "上海"),
    ("兰州", "西安"),
    ("昆明", "广州"),
    ("大理", "昆明"),
    ("北京", "大连"),
    ("大连", "沈阳"),
    ("太原", "北京"),
    ("呼和浩特", "北京"),
    ("拉萨", "成都"),
    ("天津", "上海"),
]

# 指定目标航司（统一小写）
TARGET_AIRLINES = {
    "ca", "mu", "cz", "hu", "zh", "sc", "3u", "fm", "mf", "kn", "8l",
    "gy", "9c", "ho", "jd", "ns", "pn", "ky", "g5", "a6", "eu", "uq",
    "bk", "dr", "gl", "y8", "lt", "tv"
}

# 判断是否为 Windows
IS_WINDOWS = platform.system().lower() == "windows"

# 设置代理（仅在 Linux 使用）
PROXIES = {
    "http": "http://*************:4780",
    "https": "http://*************:4780"
}

async def fetch_flight(org: str, dst: str, depart_date: datetime.date):
    url = "http://************:11111/tripnow"
    traceid = str(uuid.uuid4().hex)
    payload = {
        "topk": 5,
        "traceid": traceid,
        "domain": "tripnow2",
        "extendQuery": {
            "查询类型": "dynamic",
            "dynamicQuery": {
                "flightNo": '',
                "dst": dst,
                "org": org,
                "departDate": depart_date.strftime("%Y-%m-%d"),
            },
        },
    }

    try:
        if IS_WINDOWS:
            response = requests.post(url, json=payload, proxies=PROXIES, timeout=10)
        else:
            response = requests.post(url, json=payload, timeout=10)
        response.raise_for_status()
        resp_json = response.json()
        res = resp_json.get("dynamic", {}).get("data", [])
        return res
    except Exception as e:
        print(f"[ERROR] 查询 {org} → {dst} {depart_date} 失败: {e}")
        return []

async def gather_flights_for_day(date: datetime.date):
    filtered_flights = []
    for org, dst in TARGET_ROUTES:
        flights = await fetch_flight(org, dst, date)
        for flight in flights:
            content = flight.get("content", {})
            flight_no = content.get("航班号", "").lower()
            if any(flight_no.startswith(code) for code in TARGET_AIRLINES):
                filtered_flights.append(content)

    return filtered_flights

def save_to_json(data: list, date: datetime.date):
    filename = f"flight_test_data{date.strftime('%Y-%m-%d')}.json"
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print(f"[OK] 保存: {filename}")

async def main(n_days: int):
    today = datetime.date.today()
    for i in range(n_days):
        date = today + datetime.timedelta(days=i)
        print(f"[INFO] 查询日期: {date}")
        flights = await gather_flights_for_day(date)
        save_to_json(flights, date)

if __name__ == "__main__":
    days = 2  # 查询未来3天
    asyncio.run(main(days))
