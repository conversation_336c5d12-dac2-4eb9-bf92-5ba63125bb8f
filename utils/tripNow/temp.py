from datetime import datetime

import requests
import time
start=time.time()
base_url = "http://***********:14414?cmd=aichat"
base_url = "http://localhost:14414?cmd=aichat"


# 将参数编码为URL格式z
data = {
    "domain":"tripnow2",
    "msg":"",
    "sessionId": '2507131546' ,
    "model":"DeepSeekV3",
    "onlyResult":"true",
    # "stream": "true",
    "phoneid": "999"
}

proxies = {
    # "http": "http://*************:4780",    "https": "http://*************:4780"
}

arr = [r"赶不上飞机了怎么办"]
for item in arr:
    data.update({"msg": item})
    response = requests.post(base_url, data=data, proxies=proxies)
    print(response.text)