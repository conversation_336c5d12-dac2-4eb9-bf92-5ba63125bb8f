import time

from zhipuai import ZhipuAI

client = ZhipuAI(api_key="ce56e5bdf00a46ccbf38fc429ee839a1.nXs7VVm1yb8pRGZN")  # 填写您自己的APIKey



start_time = time.time()
response = client.web_search.web_search(
    search_engine="search_pro",
    search_query="国航宠物进机舱的尺寸限制是什么",
    count=1,  # 返回结果的条数，范围1-50，默认10
    # search_domain_filter="www.sohu.com",  # 只访问指定域名的内容
    search_recency_filter="noLimit",  # 搜索指定日期范围内的内容
    content_size="high"  # 控制网页摘要的字数，默认medium
)

elapsed_time = (time.time() - start_time) * 1000  # 转换为毫秒
print(f"\n⏱ 调用耗时：{elapsed_time:.2f} ms\n")
print(response)