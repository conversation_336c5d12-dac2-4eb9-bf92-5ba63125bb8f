import time

import requests
import json

url = "https://api.bochaai.com/v1/web-search"

payload = json.dumps({
  "query": "国航托运宠物需要提供哪些健康证明材料？",
  "summary": True,
  "count": 1
})

headers = {
  'Authorization': 'Bearer sk-58154ea2e51b477e9b0825348649fdf9',
  'Content-Type': 'application/json'
}

start_time = time.time()
response = requests.request("POST", url, headers=headers, data=payload)
elapsed_time = (time.time() - start_time) * 1000  # 转换为毫秒
print(f"\n⏱ 调用耗时：{elapsed_time:.2f} ms\n")
print(response.text)