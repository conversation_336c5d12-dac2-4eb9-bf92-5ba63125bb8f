import json

import requests
url = "http://localhost:14414?cmd=getsession"
url = "http://***********:14414?cmd=getsession"
# url = "http://**********:13313?cmd=getsession"
# url = "http://***********:13313?cmd=getsession"
data = {
        "sessionId":"20250901191134",
            "domain":"huawei",
    }
proxies = {
    "http": "http://*************:4780",
    "https": "http://*************:4780"
}
if "localhost" in url or "192.168" in url or "https" in url:
    response = requests.post(url, json=data)
else:
    response = requests.post(url, json=data, proxies=proxies)

print(response.text)
res_json = response.json()

# 提取所有 content 字段
contents = [
    item["content"]
    for item in res_json.get("data", {}).get("userHistory", [])
    if "content" in item
]

# 输出结果
print(json.dumps(contents, ensure_ascii=False, indent=2))