import json
import pandas as pd
# import pycryptodome
# 原始 JSON 字符串（可能混有 JS 风格语法）
json_str = '''
[
    {
        "date": "2025-06-15",
        "b": "2025-06-15T00:00:00",
        "e": "2025-06-16T00:00:00",
        "region": "中国大陆",
        "userIpInfo": null,
        "guestIpInfo": null,
        "registerCount": 10,
        "userCount": 1333,
        "appUserCount": 20,
        "appGuestCount": 36,
        "h5UserCount": 2,
        "h5GuestCount": 7,
        "newGuestCount": 33,
        "accessedGuestCount": 44,
        "orderPageNewUserCount": 6,
        "orderPageUserCount": 9,
        "preCommitNewUserCount": 2,
        "preCommitUserCount": 5,
        "orderNewUserCount": 1,
        "orderUserCount": 3,
        "payNewUserCount": 1,
        "firstPayUserCount": 1,
        "notFirstPayUserCount": 1,
        "c1": 0,
        "c2": 0,
        "payUserCount": 3,
        "xhsIosCount": null,
        "xhsAndroidCount": null,
        "xhsH5Count": null,
        "appAvgDuration": null,
        "mealSetClickCount": 0,
        "mealSetClickUserCount": 0,
        "restDetailLoadCount": 56,
        "restDetailLoadUserCount": 19,
        "mapBookClickCount": 6,
        "mapBookClickUserCount": 2,
        "mapListBookClickCount": 0,
        "mapListBookClickUserCount": 0,
        "mapSearchCallCount": 303,
        "mapSearchCallUserCount": 8
    },
    {
        "date": "2025-06-15",
        "b": "2025-06-15T00:00:00",
        "e": "2025-06-16T00:00:00",
        "region": "港澳台",
        "userIpInfo": null,
        "guestIpInfo": null,
        "registerCount": 0,
        "userCount": 71,
        "appUserCount": 0,
        "appGuestCount": 2,
        "h5UserCount": 0,
        "h5GuestCount": 1,
        "newGuestCount": 3,
        "accessedGuestCount": 3,
        "orderPageNewUserCount": 0,
        "orderPageUserCount": 0,
        "preCommitNewUserCount": 0,
        "preCommitUserCount": 0,
        "orderNewUserCount": 0,
        "orderUserCount": 0,
        "payNewUserCount": 0,
        "firstPayUserCount": 0,
        "notFirstPayUserCount": 0,
        "c1": 0,
        "c2": 0,
        "payUserCount": 0,
        "xhsIosCount": null,
        "xhsAndroidCount": null,
        "xhsH5Count": null,
        "appAvgDuration": null,
        "mealSetClickCount": 0,
        "mealSetClickUserCount": 0,
        "restDetailLoadCount": 7,
        "restDetailLoadUserCount": 1,
        "mapBookClickCount": 0,
        "mapBookClickUserCount": 0,
        "mapListBookClickCount": 0,
        "mapListBookClickUserCount": 0,
        "mapSearchCallCount": 0,
        "mapSearchCallUserCount": 0
    },
    {
        "date": "2025-06-15",
        "b": "2025-06-15T00:00:00",
        "e": "2025-06-16T00:00:00",
        "region": "国外",
        "userIpInfo": null,
        "guestIpInfo": null,
        "registerCount": 2,
        "userCount": 216,
        "appUserCount": 3,
        "appGuestCount": 8,
        "h5UserCount": 0,
        "h5GuestCount": 7,
        "newGuestCount": 14,
        "accessedGuestCount": 15,
        "orderPageNewUserCount": 2,
        "orderPageUserCount": 2,
        "preCommitNewUserCount": 1,
        "preCommitUserCount": 1,
        "orderNewUserCount": 1,
        "orderUserCount": 1,
        "payNewUserCount": 1,
        "firstPayUserCount": 0,
        "notFirstPayUserCount": 0,
        "c1": 1,
        "c2": 0,
        "payUserCount": 1,
        "xhsIosCount": null,
        "xhsAndroidCount": null,
        "xhsH5Count": null,
        "appAvgDuration": null,
        "mealSetClickCount": 0,
        "mealSetClickUserCount": 0,
        "restDetailLoadCount": 9,
        "restDetailLoadUserCount": 5,
        "mapBookClickCount": 0,
        "mapBookClickUserCount": 0,
        "mapListBookClickCount": 0,
        "mapListBookClickUserCount": 0,
        "mapSearchCallCount": 0,
        "mapSearchCallUserCount": 0
    },
    {
        "date": "2025-06-16",
        "b": "2025-06-16T00:00:00",
        "e": "2025-06-17T00:00:00",
        "region": "中国大陆",
        "userIpInfo": null,
        "guestIpInfo": null,
        "registerCount": 11,
        "userCount": 1344,
        "appUserCount": 25,
        "appGuestCount": 51,
        "h5UserCount": 1,
        "h5GuestCount": 23,
        "newGuestCount": 64,
        "accessedGuestCount": 75,
        "orderPageNewUserCount": 7,
        "orderPageUserCount": 17,
        "preCommitNewUserCount": 3,
        "preCommitUserCount": 8,
        "orderNewUserCount": 3,
        "orderUserCount": 7,
        "payNewUserCount": 2,
        "firstPayUserCount": 1,
        "notFirstPayUserCount": 2,
        "c1": 0,
        "c2": 0,
        "payUserCount": 5,
        "xhsIosCount": null,
        "xhsAndroidCount": null,
        "xhsH5Count": null,
        "appAvgDuration": null,
        "mealSetClickCount": 0,
        "mealSetClickUserCount": 0,
        "restDetailLoadCount": 78,
        "restDetailLoadUserCount": 28,
        "mapBookClickCount": 0,
        "mapBookClickUserCount": 0,
        "mapListBookClickCount": 0,
        "mapListBookClickUserCount": 0,
        "mapSearchCallCount": 85,
        "mapSearchCallUserCount": 10
    },
    {
        "date": "2025-06-16",
        "b": "2025-06-16T00:00:00",
        "e": "2025-06-17T00:00:00",
        "region": "港澳台",
        "userIpInfo": null,
        "guestIpInfo": null,
        "registerCount": 0,
        "userCount": 71,
        "appUserCount": 0,
        "appGuestCount": 1,
        "h5UserCount": 0,
        "h5GuestCount": 0,
        "newGuestCount": 0,
        "accessedGuestCount": 1,
        "orderPageNewUserCount": 0,
        "orderPageUserCount": 0,
        "preCommitNewUserCount": 0,
        "preCommitUserCount": 0,
        "orderNewUserCount": 0,
        "orderUserCount": 0,
        "payNewUserCount": 0,
        "firstPayUserCount": 0,
        "notFirstPayUserCount": 0,
        "c1": 0,
        "c2": 0,
        "payUserCount": 0,
        "xhsIosCount": null,
        "xhsAndroidCount": null,
        "xhsH5Count": null,
        "appAvgDuration": null,
        "mealSetClickCount": 0,
        "mealSetClickUserCount": 0,
        "restDetailLoadCount": 0,
        "restDetailLoadUserCount": 0,
        "mapBookClickCount": 0,
        "mapBookClickUserCount": 0,
        "mapListBookClickCount": 0,
        "mapListBookClickUserCount": 0,
        "mapSearchCallCount": 0,
        "mapSearchCallUserCount": 0
    },
    {
        "date": "2025-06-16",
        "b": "2025-06-16T00:00:00",
        "e": "2025-06-17T00:00:00",
        "region": "国外",
        "userIpInfo": null,
        "guestIpInfo": null,
        "registerCount": 1,
        "userCount": 217,
        "appUserCount": 6,
        "appGuestCount": 11,
        "h5UserCount": 1,
        "h5GuestCount": 3,
        "newGuestCount": 11,
        "accessedGuestCount": 14,
        "orderPageNewUserCount": 1,
        "orderPageUserCount": 5,
        "preCommitNewUserCount": 1,
        "preCommitUserCount": 3,
        "orderNewUserCount": 1,
        "orderUserCount": 2,
        "payNewUserCount": 1,
        "firstPayUserCount": 0,
        "notFirstPayUserCount": 1,
        "c1": 0,
        "c2": 0,
        "payUserCount": 2,
        "xhsIosCount": null,
        "xhsAndroidCount": null,
        "xhsH5Count": null,
        "appAvgDuration": null,
        "mealSetClickCount": 0,
        "mealSetClickUserCount": 0,
        "restDetailLoadCount": 23,
        "restDetailLoadUserCount": 11,
        "mapBookClickCount": 2,
        "mapBookClickUserCount": 1,
        "mapListBookClickCount": 0,
        "mapListBookClickUserCount": 0,
        "mapSearchCallCount": 14,
        "mapSearchCallUserCount": 2
    }
]
'''

# 替换 JS 风格为 Python 风格
json_str_fixed = json_str.replace('null', 'None').replace('true', 'True').replace('false', 'False')
data = eval(json_str_fixed)

# 自定义字段处理
def process_item(item):
    if item.get("b"):
        item["b"] = item["b"][:10]
    if item.get("e"):
        item["e"] = item["e"][:10]
    if item.get("date") is None:
        item["date"] = item.get("b")
    return item

# 自定义表头映射（只导出这些字段）
header_map = {
    "date": "周期",
    "region": "用户来源",
    "registerCount": "当日新增注册用户数",
    "userCount": "截止填写日期，总注册用户数",

    "appUserCount":"日启动Dings APP用户数 注册用户",
    "appGuestCount":"日启动Dings APP用户数 游客",
    "h5UserCount":"日启动h5用户数 注册用户",
    "h5GuestCount":"日启动h5用户数 游客",
    "newGuestCount":"当日新增游客数",
    "accessedGuestCount":"当天活跃游客数（新增+旧游客）",
    "orderPageNewUserCount":"加载创建订单页用户数（新增）",
    "orderPageUserCount":"加载创建订单页用户数（总数）",
    "preCommitNewUserCount":"当日点击“立即订座”用户数",
    "preCommitUserCount": "当日点击“立即订座”用户数（总数）",

    "orderNewUserCount": "当日创建订单用户数（新增）",
    "orderUserCount": "当日创建订单用户数（总数）",
    "payNewUserCount": "当日新增付费用户数（当日注册当日付费）",
    "firstPayUserCount": "首次付费用户数（旧用户即非当日注册）",
    "c1": "二次付费用户数（新用户，今天注册后，二次付费） ",
    "c2": "二次付费用户数（旧用户，今天首单后二次下单） ",
    "notFirstPayUserCount": "二次付费用户数（旧用户，首单非今天，今天二次下单） ",
    "payUserCount": "当日总付费用户（新用户+旧用户）"
}

# 只保留 header_map 里定义的字段
processed_data = []
for item in data:
    item = process_item(item)
    filtered_item = {k: item.get(k) for k in header_map.keys()}
    processed_data.append(filtered_item)

# 导出
df = pd.DataFrame(processed_data)
df.rename(columns=header_map, inplace=True)
df.to_excel("output_filtered.xlsx", index=False)

print("导出完成：output_filtered.xlsx")