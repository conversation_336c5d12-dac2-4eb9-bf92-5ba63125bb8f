

    # def get_dirs(self, token, domain):
    #     """
    #     获取指定目录下的所有文件及文件夹信息。
    #
    #     :param token: 目录的 token
    #     :param domain: 相关域名
    #     :return: 文件和文件夹列表
    #     """
    #     if domain and not check_is_download(token, domain):
    #         return []
    #
    #     file_names = []
    #     page_token = None
    #     while True:
    #         url = f"https://open.feishu.cn/open-apis/drive/v1/files?direction=DESC&folder_token={token}&order_by=EditedTime"
    #         if page_token:
    #             url += f"&page_token={page_token}"
    #         response = requests.get(url, headers=self.headers)
    #         json_data = response.json()
    #         logger.info("获取到的文件信息：{}".format(json_data))
    #
    #         for dir_data in json_data["data"]["files"]:
    #             if dir_data["type"] == "folder":
    #                 file_names += self.get_dirs(dir_data["token"], domain)
    #             elif dir_data["type"] in ["sheet", "file"]:
    #                 file_names.append(dir_data)
    #
    #         if not json_data["data"]["has_more"]:
    #             break
    #
    #         page_token = json_data["data"].get("next_page_token")
    #     return file_names
    #
    # def create_export_task(self, token):
    #     """
    #     创建一个导出任务。
    #
    #     :param token: 文件的 token
    #     :return: 任务 ticket
    #     """
    #     url = "https://open.feishu.cn/open-apis/drive/v1/export_tasks"
    #     payload = json.dumps({
    #         "file_extension": "xlsx",
    #         "token": token,
    #         "type": "sheet"
    #     })

import json

import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *

from utils.feishu_sdk import FeishuSDK
# from Crypto.Cipher import AES



    # SDK 使用说明: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/server-side-sdk/python--sdk/preparations-before-development
# 以下示例代码默认根据文档示例值填充，如果存在代码问题，请在 API 调试台填上相关必要参数后再复制代码使用
# 复制该 Demo 后, 需要将 "YOUR_APP_ID", "YOUR_APP_SECRET" 替换为自己应用的 APP_ID, APP_SECRET.
def main():
    fs = FeishuSDK()
    app_token = fs.get_app_access_token()
    print(app_token)
    # # 创建client
    # client = lark.Client.builder() \
    #     .app_id("cli_a4546270ea38900e") \
    #     .app_secret("QrF2zDOPvj6Rc9fX81s99FG5GfBd6lez") \
    #     .log_level(lark.LogLevel.DEBUG) \
    #     .build()
    #
    # # 构造请求对象
    # request: SearchAppTableRecordRequest = SearchAppTableRecordRequest.builder() \
    #     .app_token(app_token) \
    #     .table_id("tbl0xe5g8PP3U3cS") \
    #     .user_id_type("open_id") \
    #     .page_size(10) \
    #     .build()
    #
    # # 发起请求
    # response: SearchAppTableRecordResponse = client.bitable.v1.app_table_record.search(request)
    #
    # # 处理失败返回
    # if not response.success():
    #     lark.logger.error(
    #         f"client.bitable.v1.app_table_record.search failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
    #     return
    #
    # # 处理业务结果
    # lark.logger.info(lark.JSON.marshal(response.data, indent=4))





if __name__ == "__main__":
    main()