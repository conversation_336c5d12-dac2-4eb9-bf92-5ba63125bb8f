import re
from collections import defaultdict
from datetime import datetime

# 忽略的 userId 列表
ignored_user_ids = set([
    "udbd8cab48af4426fbd7e65bf17784bc1", "ue77a0157ef374949b34796d216e1e8f9", "u62857ec9ea654155a50d05621c26b6a8",
    "u4b0124d946ba45fdbf4c885c4e4aa4e9", "u48730a32a83b4aeda895c6c8d2daee3b", "u4ac843247cdd4c8aa87eb7c772fee165",
    "ufaa25e8e109f42bd901b7c7c24c69aa3", "u480a91020756468597af1faa6f326892", "uc1dbbfa0c3a1404ba7119d94b2180725",
    "u05843567d8144a0eb6c95526fea7a48a", "u2cd5287fe1ab4b45a5087d01c08af128", "u5a1c5a03bded4a43b3b17a5a3f6c1e4f",
    "u323aba1eede7446d8ebde2d3f516c33f", "u0c560a84a51642bdafe8672c92866901", "udc9580affc8143079b38b5c30591def4",
    "uc48a2de94e624c5989b8aa0ff9196f85", "u884d2c2fc9cc45d9913c591574eaa64f", "ue1e5b7a5d758433887c184585e1cd1c0",
    "u4a21058f999545b6aaa317297d9c154f", "uf155f1dfcd9e49b9bdb5397e40f423a5", "u1f3b7ac15ab3494587d8a35ce1848b4f",
    "uc771e57d571842f6a4282e8a193e4b9c", "u3ef5d51509d34f15959a8591823c811f", "udf7f19aa84bf4544a548b8e2c56c1a38",
    "uafbecb3e949a41ff88e89e89c5d3e64c", "u6cb0aef3d13b406e97a7791c178e5bd3", "u63cac06f727c4352891a988938a5d30c",
    "u3899df167b3948cfa3a003c67a131094"
])

# 日志文件路径
log_file = r"C:\Users\<USER>\Desktop\mapSearchCount.txt"  # 替换成你的实际文件名


# 正则
datetime_pattern = re.compile(r'^(\d{4}-\d{2}-\d{2})T')
user_id_pattern = re.compile(r'"userId"\s*:\s*"([^"]+)"')

# 统计结构
date_total_count = defaultdict(int)
date_user_count = defaultdict(lambda: defaultdict(int))
date_unique_users = defaultdict(set)

with open(log_file, 'r', encoding='utf-8') as f:
    for line in f:
        date_match = datetime_pattern.search(line)
        user_match = user_id_pattern.search(line)
        if not date_match or not user_match:
            continue
        date = date_match.group(1)
        user_id = user_match.group(1)
        if user_id in ignored_user_ids:
            continue

        date_total_count[date] += 1
        date_user_count[date][user_id] += 1
        date_unique_users[date].add(user_id)

# 输出总数
print("=== 每日总请求数 ===")
for date in sorted(date_total_count):
    print(f"{date}: {date_total_count[date]}")

# 输出每日 + 用户
print("\n=== 每日 + 用户请求数 ===")
for date in sorted(date_user_count):
    print(f"\n{date}:")
    for user_id, count in sorted(date_user_count[date].items(), key=lambda x: -x[1]):
        print(f"  {user_id}: {count}")

# 输出每日去重用户数
print("\n=== 每日去重用户数 ===")
for date in sorted(date_unique_users):
    print(f"{date}: {len(date_unique_users[date])}")