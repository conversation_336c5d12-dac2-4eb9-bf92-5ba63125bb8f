import datetime

import requests
import time
start=time.time()
# 定义接口的基本U
base_url = "http://***********:14414?cmd=aichatHW"
# base_url = "http://**********:13313?cmd=aichatHW"
# base_url = "http://localhost:14414?cmd=aichatHW"


# 将参数编码为URL格式z
data = {
    "cmd": "aichatHW",
    "content": "{\"original\":\"北京到上海的火车\",\"messages\":[{\"role\":\"user\",\"content\":\"北京到上海的火车\"}]}",
    "deviceInfo": "{\"deviceType\":0,\"phoneType\":\"ALN-AL80\",\"deviceFormation\":\"HDSpeaker\",\"prdVer\":\"11.3.5.300\",\"countryCode\":\"CN\",\"sysVer\":\"OpenHarmony-*********\",\"time\":\"20250722105657223\",\"locale\":\"zh-CN\",\"deviceId\":\"906603cb1a8ca804b6a4e435af220082d56a758e1d3ec4a163de5f1b0d65b88e\"}",
    "messages": "[{\"role\":\"user\",\"content\":\"北京到上海的火车\"}]",
    "original": "北京到上海的火车",
    "pagination": "{\"limit\":5,\"start\":\"\"}",
    "session": "{\"interactionId\":\"71\",\"conversationId\":\"11e7cbf5eccf4b1b8075cbc6b7daf7f0\",\"isNew\":false,\"sessionId\":\"f7abac9b-332b-4187-84a3-45068f79de18\"}",
    "userAuth": "{\"user\":{\"userId\":\"\"}}",
    "utterance": "{\"original\":\"北京到上海的火车\",\"type\":\"text\"}",
    "version": "1.0"
}

proxies = {
    "http": "http://*************:4780",    "https": "http://*************:4780"
}

# if base_url.__contains__("localhost") or base_url.__contains__("192.168"):
#     response = requests.post(base_url, data=data)
# else:
#     response = requests.post(base_url, data=data, proxies=proxies)
# end=time.time()
# print(end-start)
# print(response.text)



if "localhost" in base_url or "192.168" in base_url:
    response = requests.post(base_url, data=data)
else:
    response = requests.post(base_url, data=data, proxies=proxies)

try:
    print(response.text)
except Exception as e:
    content = f"[解析失败] {e}"

print("\n" + "=" * 60)
print(f"🟡 系统回复：\n")
print("=" * 60 + "\n")
