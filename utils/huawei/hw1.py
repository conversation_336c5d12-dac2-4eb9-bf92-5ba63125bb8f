#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import requests
import time

heads = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "sign": "KatjoDDGALqpDIRQE2XP+cA+76IcWvPMJ0TpyzhLdk4=",
    "accesskey": "ecedb8640f044d038530b80650868fe",
    "ts": "1732522269233"
}

data = {
    "pagination": {"limit": 5, "start": ""},
    "domain": "huawei",
    "ingredient": "明天呢？",
    "userAuth": {},
    "version": "1.0",
    "content": {"ingredient": "飞机延误了怎么办"},
    "deviceInfo": {
        "deviceType": 0,
        "phoneType": "BLM-00",
        "timezone": "GMT+08:00",
        "sysVer": "EmotionUI_11.1.0",
        "locale": "zh_CN",
        "deviceId": "81fe22fe465185ab106eedb83f4947247a1007a19827b923a4f3599635af5812",
        "manufacturer": "HUAWEI",
        "supportFeatureList": [{"featureType": "CONTENT_CARD", "featureVersion": "12.0"}],
        "deviceFormation": "HDSpeaker",
        "prdVer": "11.1.48.301",
        "countryCode": "ZH_CN",
        "time": "20241023091411469",
        "oaid": ""
    },
    "utterance": {"original": "ca101？", "type": "text"},
    "sessionId": "123"
}

url = "http://localhost:14414/?cmd=aichatHW"
url ="https://dings.133.cn/cgi/agi/gpt/tripnow"
start = time.time()
proxies = None
with requests.post(
    url,
    json=data,
    headers=heads,proxies=proxies,
    stream=True,
    timeout=(5, 60)  # 连接、读取超时，防卡死
) as response:
    response.raise_for_status()
    # 按行流式读取
    for line in response.iter_lines(decode_unicode=True):
        if line:  # 避免空行
            print(line)

end = time.time()
print("耗时:", end - start, "秒")