import datetime
import requests

base_url = "http://***********:14414?cmd=aichat"
# base_url = "http://**********:13313?cmd=aichat"
# base_url = "http://localhost:14414?cmd=aichat"


headers = {
    "Content-Type": "application/json"
}

proxies = {
    "http": "http://*************:4780",
    "https": "http://*************:4780"
}

arr = ['明天北京飞上海'] # 齐齐哈尔到乌鲁木齐的火车

for idx, item in enumerate(arr, start=1):
    body = {
        "msg": item,
        "sessionId": datetime.datetime.now().strftime("%Y%m%d%H%M%S"),
        # "channel": "光帆",
        "stream": "true",
        "domain": "huawei"
    }

    if "localhost" in base_url or "192.168" in base_url or "https" in base_url:
        response = requests.post(base_url, json=body, headers=headers, stream=True)
    else:
        response = requests.post(base_url, json=body, headers=headers, proxies=proxies, stream=True)

    print("\n" + "=" * 60)
    print(f"🟢 用户输入（第 {idx} 条）: {item}\n")
    print(f"🟡 系统回复（流式）：\n")

    for line in response.iter_lines(decode_unicode=True):
        if line:
            print(line, flush=True)

    print("=" * 60 + "\n")
