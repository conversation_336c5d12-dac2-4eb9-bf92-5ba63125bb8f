import requests
import uuid

url = "http://************:10001/tripnow"
proxy = {
    "http": "http://*************:4780",
    "https": "http://*************:4780"
}

traceid = str(uuid.uuid4())
depart_date = "2025-07-23"

payload = {
    "topk": 5,
    "extendQuery": {
        "trainQuery": {
            "flightNo": "",
            "dst": "北京",
            "org": "上海",
            "departDate": depart_date
        },
        "动态类型": "火车",
        "dst": "北京",
        "application": "",
        "org": "上海",
        "domain": "huawei",
        "出发日期": depart_date,
        "查询类型": "dynamic",
        "businessType": "",
        "航班/列车号": ""
    },
    "traceid": traceid,
    "esQuery": "后天从上海到北京  火车 ",
    "query": "后天从上海到北京, 火车？",
    "domain": "huawei"
}

headers = {
    "Content-Type": "application/json"
}

try:
    response = requests.post(
        url,
        json=payload,
        headers=headers,
        proxies=proxy,
        timeout=(2, 10)  # (connect timeout, read timeout)
    )
    print("Status:", response.status_code)
    print("Response:", response.text)
except Exception as e:
    print("Request failed:", e)
