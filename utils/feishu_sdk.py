import json
import os
import sys
import time
import pandas as pd
import requests
from retrying import retry
from loguru import logger
from requests_toolbelt import MultipartEncoder


class FeishuSDK:
    def __init__(self):
        """
        初始化 FeishuSDK 实例，获取应用的访问令牌并设置请求头。
        """
        self.access_token = self.get_app_access_token()
        self.headers = {'Authorization': f'Bearer {self.access_token}'}
        self.json_headers = {'Content-Type': 'application/json',
                             'Authorization': f'Bearer {self.access_token}'}

    def get_app_access_token(self):
        """
        获取 Feishu 应用的访问令牌。

        :return: 访问令牌
        """
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        payload = json.dumps({
            "app_id": "cli_a4546270ea38900e",
            "app_secret": "QrF2zDOPvj6Rc9fX81s99FG5GfBd6lez"
        })
        headers = {'Content-Type': 'application/json'}
        response = requests.post(url, headers=headers, data=payload)
        return response.json()["tenant_access_token"]

    def refresh_token(self):
        """
        刷新 Feishu 访问令牌。

        :return: 刷新后的令牌
        """
        url = "https://open.feishu.cn/open-apis/authen/v1/refresh_access_token"
        payload = json.dumps({
            "grant_type": "refresh_token",
            "refresh_token": "ur-eb0TCWkmp8C8.HeNob1C0g4k5Re5glzzjgw04hq82KWM"
        })
        headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer t-g1048ogzGF2FCZRUWBPTVNQ3VBWR5GYVFQNOPT6O'
        }
        response = requests.post(url, headers=headers, data=payload)
        logger.info(response.json())
        return response.json()["data"]["refresh_token"]