import datetime

import requests
import time
start=time.time()
# 定义接口的基本U
base_url = "http://***********:14414?cmd=aichat"
# base_url = "http://**********:13313?cmd=aichat"
base_url = "http://localhost:14414?cmd=aichat"
# base_url = "http://**********:13314?cmd=aichat"


# 将参数编码为URL格式z
data = {
    "domain":"lumiPods",#huawei
    "msg":"",
    "sessionId": datetime.datetime.now().strftime("%Y%m%d%H%M%S"),
    "onlyResult":"true",
    # "stream": "true",
    # "phoneid": "0",
    # "test_mode": "有行程测试",
    # "traceid": "on9yjstiioz89u30"
}

proxies = {
    "http": "http://*************:4780",    "https": "http://*************:4780"
}

# if base_url.__contains__("localhost") or base_url.__contains__("192.168"):
#     response = requests.post(base_url, data=data)
# else:
#     response = requests.post(base_url, data=data, proxies=proxies)
# end=time.time()
# print(end-start)
# print(response.text)


arr = ['明天北京飞南昌']  # 示例输入
for idx, item in enumerate(arr, start=1):
    data.update({"msg": item})

    if "localhost" in base_url or "192.168" in base_url:
        response = requests.post(base_url, data=data)
    else:
        response = requests.post(base_url, data=data, proxies=proxies)

    try:
        content = response.json()["data"]["userHistory"]["content"]
        print(response.text)
    except Exception as e:
        content = f"[解析失败] {e}"

    print("\n" + "=" * 60)
    print(f"🟢 用户输入（第 {idx} 条）: {item}\n")
    print(f"🟡 系统回复：\n")
    print(content.strip())
    print("=" * 60 + "\n")
