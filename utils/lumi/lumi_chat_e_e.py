import datetime
import requests

# 测试地址
base_url = "https://dingstest.133.cn/aichate"
# 用户输入的对话内容
arr = ['国航宠物进机舱的尺寸限制是什么']
# 商务提供
channel = '雷鸟'
# 会话 ID。不传值则默认新建，会话 ID 使用 UUID 生成。多轮对话必须传值。
sessionId = channel + datetime.datetime.now().strftime("%Y%m%d%H%M%S")

headers = {"Content-Type": "application/json"}
for idx, item in enumerate(arr, start=1):
    body = {
        "msg": item,
        "sessionId": sessionId,
        "channel": channel,
        "stream": "true"
    }
    response = requests.post(base_url, json=body, headers=headers, stream=True)
    print("\n" + "=" * 60)
    print(f"用户输入（第 {idx} 条）: {item}\n")
    print(f"系统回复:\n")
    for line in response.iter_lines(decode_unicode=True):
        if line:
            print(line, flush=True)
    print("=" * 60 + "\n")