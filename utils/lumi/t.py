def get_bailian_model_response(messages, model="qwen0.6B", mode="text", temperature=0.2):
    DASHSCOPE_API_KEY = "sk-fb36d99bbb4c4c25bc6c3306937e980b"
    # url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
    url = "http://120.133.63.163:9000/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {DASHSCOPE_API_KEY}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        # "enable_thinking": False,
        "chat_template_kwargs": {"enable_thinking": False}
    }
    count = 0
    while count < 3:
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=500)
            break
        except Exception as e:
            logger.error(f"Error: {e}")
            count += 1
            continue

    # print(f"Response status code: {response.status_code}")
    if response.status_code == 200:
        response_data = response.json()
        # print(f"Response data: {response_data}")
        if "choices" in response_data and len(response_data["choices"]) > 0:
            result_content = response_data["choices"][0]["message"]["content"]
            if mode == "json":
                try:
                    result_content = json.loads(result_content)
                except json.JSONDecodeError:
                    # logger.warning(f"json解析失败，尝试修复json字符串")
                    PATTERN = re.compile(r"```(?:json\s+)?(\W.*?)```", re.DOTALL)
                    action_match = PATTERN.search(result_content)
                    if action_match is not None:
                        json_text, json_object = try_parse_json_object(action_match.group(1).strip())
                        result_content = json_object
                    if type(result_content) != dict:
                        result_content = {}
                return result_content
            elif mode == "text":
                return result_content
            else:
                logger.error(f"Unsupported mode: {mode}")
                raise ValueError(f"Unsupported mode: {mode}")
        else:
            logger.error("No choices found in the response.")
            raise Exception("No choices found in the response.")
    else:
        logger.error(f"Request failed with status code {response.status_code}: {response.text}")
        raise Exception(f"Request failed with status code {response.status_code}: {response.text}")