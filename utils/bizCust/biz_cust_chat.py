import datetime
import requests

headers = {
    "Content-Type": "application/json"
}
proxies = {
    "http": "http://*************:4780",
    "https": "http://*************:4780"
}
scenic_spot='昙华林'
samples = ''
system_prompt = (
    f"回答问题"
)
user_prompt_arr = ["你好", "哈哈"]

base_url = "http://localhost:14414?cmd=aichat"
#测试
base_url = "http://***********:14414?cmd=aichat"
#正式
# base_url = "http://**********:13313?cmd=aichat"
#会话id，不传值则默认新建会话，新建sessionId用uuid生成。后续多需要多轮对话必须传值
sessionId = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
#stream=true时以流式(打字机效果)应答返回，并且应答的Content-Type为：text/event-stream; charset=utf-8
stream = False
model = 'DeepSeekV3'

for idx, item in enumerate(user_prompt_arr, start=1):
    body = {
        # "msg": item,
        "system_prompt": system_prompt,
        "sessionId": sessionId,
        "stream": stream,
        "domain": "bizCust",
        "onlyResult": True,
        "model": model
    }

    request_args = {
        "url": base_url,
        "json": body,
        "headers": headers,
        "stream": stream
    }

    if "localhost" not in base_url and "192.168" not in base_url and "https" not in base_url:
        request_args["proxies"] = proxies

    response = requests.post(**request_args)

    print("\n" + "=" * 60)
    print(f"用户输入（第 {idx} 条）: {item}\n")
    print("系统回复：\n")

    if stream:
        for line in response.iter_lines(decode_unicode=True):
            if line:
                print(line, flush=True)
    else:
        try:
            print(response.json()['data']['userHistory']['content'])
        except Exception as e:
            print(f"[非流式返回解析失败] 错误：{e}")
            print(response.text)

    print("=" * 60 + "\n")