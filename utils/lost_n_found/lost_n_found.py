import datetime

import requests
import time
start=time.time()
# 定义接口的基本U
base_url = "http://***********:14414?cmd=aichat"
base_url = "http://**********:13313?cmd=aichat"
base_url = "http://***********:13313?cmd=aichat"
base_url = "http://localhost:14414?cmd=aichat"


# 将参数编码为URL格式z
data = {
    "domain":"tripnow2",
    "msg":"",
    "sessionId": '2508181426',
    # "sessionId": datetime.datetime.now().strftime("%Y%m%d%H%M%S"),
    "onlyResult":"true",
    # "stream": "true",
    "phoneid": "26762163",
    # "test_mode": "有行程测试",
    # "traceid": "on9yjstiioz89u30"
}

proxies = {
    "http": "http://*************:4780",    "https": "http://*************:4780"
}
# proxies = None

arr = [r'失物招领助手', '我丢了一个毛毯']#'失物招领助手', '你好', '我丢了一个毛毯', '今天早上', 'CA8679航班，22A', '15320206430'
for item in arr:
    data.update({"msg": item})
    if base_url.__contains__("localhost") or base_url.__contains__("192.168"):
        response = requests.post(base_url, data=data)
    else:
        response = requests.post(base_url, data=data, proxies=proxies)
    print(response.json()["data"]["userHistory"]["content"])