import numpy as np

data = [
    1581.6659927368164, 2125.441551208496, 1765.5055522918701, 1951.5621662139893,
    1783.1251621246338, 1821.0606575012207, 1483.0217361450195, 1638.1165981292725,
    1543.7469482421875, 1770.0722217559814, 1625.0951290130615, 1525.611400604248,
    1543.2863235473633, 1530.0977230072021, 1820.082664489746, 2155.033826828003,
    1454.6198844909668, 2351.306438446045, 1502.194881439209, 1625.02121925354,
    2117.5122261047363, 1940.638542175293, 1661.0493659973145, 1418.6739921569824,
    1659.6224308013916, 1521.6116905212402, 1696.0270404815674, 1791.8424606323242,
    1818.403959274292, 1759.9732875823975, 1761.1207962036133, 1811.5460872650146,
    1801.790475845337, 2017.8980827331543, 1686.7074966430664, 1825.0045776367188,
    2020.85280418396, 1957.6561450958252, 1741.6877746582031, 1971.4016914367676,
    2149.479866027832, 1998.1951713562012, 2648.9944458007812, 1606.8553924560547,
    1919.938087463379, 1777.1334648132324, 2108.5293292999268, 2464.9369716644287,
    2084.111213684082, 1881.2015056610107, 2496.9959259033203, 2165.58575630188,
    2307.394027709961, 2363.192558288574, 3038.1956100463867
]

p80 = np.percentile(data, 80)
p90 = np.percentile(data, 90)
mean = np.mean(data)

print(f"80分位数: {p80:.2f}")
print(f"90分位数: {p90:.2f}")
print(f"平均数: {mean:.2f}")
