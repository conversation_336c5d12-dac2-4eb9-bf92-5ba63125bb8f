from collections import defaultdict
from datetime import datetime
import json

# 替换为你的数据列表
data = [
    {
        "_id": "67ecf3ef3ad6ae0eb8f4ea2f",
        "restaurantOrderId": "RESORD4daf85fa6733",
        "orderCreatDate": "2025-04-02 16:23:11"
    },
    {
        "_id": "67ecfe413ad6ae0eb8f4ea32",
        "restaurantOrderId": "RESORD7b2f0b51f20e",
        "orderCreatDate": "2025-04-02 17:07:13"
    },
    {
        "_id": "67ed1ce42dc60e805a12c72f",
        "restaurantOrderId": "RESORD561302320aeb",
        "orderCreatDate": "2025-04-02 19:17:56"
    },
    {
        "_id": "67eeb410842ea0c5391499ca",
        "restaurantOrderId": "RESORDd1c5791c484e",
        "orderCreatDate": "2025-04-04 00:15:12"
    },
    {
        "_id": "67ef6424842ea0c5391499cd",
        "restaurantOrderId": "RESORD1265451769db",
        "orderCreatDate": "2025-04-04 12:46:28"
    },
    {
        "_id": "67f08917dc027359a23bcd18",
        "restaurantOrderId": "RESORDb6a04ce07cce",
        "orderCreatDate": "2025-04-05 09:36:23"
    },
    {
        "_id": "67f08c6adc027359a23bcd19",
        "restaurantOrderId": "RESORDca00f7cc001a",
        "orderCreatDate": "2025-04-05 09:50:34"
    },
    {
        "_id": "67f08d43842ea0c5391499d3",
        "restaurantOrderId": "RESORDede181566184",
        "orderCreatDate": "2025-04-05 09:54:11"
    },
    {
        "_id": "67f1f2e9dc027359a23bcd32",
        "restaurantOrderId": "RESORD7b88a80d1e3f",
        "orderCreatDate": "2025-04-06 11:20:09"
    },
    {
        "_id": "67f20848dc027359a23bcd34",
        "restaurantOrderId": "RESORDc3128765ad36",
        "orderCreatDate": "2025-04-06 12:51:20"
    },
    {
        "_id": "67f24b4edc027359a23bcd3a",
        "restaurantOrderId": "RESORD2843f9c2d18f",
        "orderCreatDate": "2025-04-06 17:37:18"
    },
    {
        "_id": "67f3184bdc027359a23bcd44",
        "restaurantOrderId": "RESORD3a501973ada3",
        "orderCreatDate": "2025-04-07 08:11:55"
    },
    {
        "_id": "67f3a7a005ad1df65a1ebd42",
        "restaurantOrderId": "RESORDb776acde22e8",
        "orderCreatDate": "2025-04-07 18:23:28"
    },
    {
        "_id": "67f3fe94882d25e8c0beab5d",
        "restaurantOrderId": "RESORD37b49dd6d4e2",
        "orderCreatDate": "2025-04-08 00:34:28"
    },
    {
        "_id": "67f67fadfe8caa2fbcff77a2",
        "restaurantOrderId": "RESORDe39ba429c86f",
        "orderCreatDate": "2025-04-09 22:09:49"
    },
    {
        "_id": "67f71ebbfe8caa2fbcff77aa",
        "restaurantOrderId": "RESORD61dc5b43bae8",
        "orderCreatDate": "2025-04-10 09:28:27"
    },
    {
        "_id": "67fc906a1f1ca1ef95137309",
        "restaurantOrderId": "RESORD5e6a4f9d8842",
        "orderCreatDate": "2025-04-14 12:34:50"
    },
    {
        "_id": "67fd02e5498b263385fa04a0",
        "restaurantOrderId": "RESORD098902fc7b48",
        "orderCreatDate": "2025-04-14 20:43:17"
    },
    {
        "_id": "67fe11861f1ca1ef9513731b",
        "restaurantOrderId": "RESORDcd7ec411ad06",
        "orderCreatDate": "2025-04-15 15:57:58"
    },
    {
        "_id": "67fe279f498b263385fa04a7",
        "restaurantOrderId": "RESORD0644e02a65a2",
        "orderCreatDate": "2025-04-15 17:32:15"
    },
    {
        "_id": "67ffbcb1498b263385fa04af",
        "restaurantOrderId": "RESORD774a51de4d09",
        "orderCreatDate": "2025-04-16 22:20:33"
    },
    {
        "_id": "68008c08498b263385fa04b6",
        "restaurantOrderId": "RESORD8b23ad671517",
        "orderCreatDate": "2025-04-17 13:05:12"
    },
    {
        "_id": "68008e971f1ca1ef95137340",
        "restaurantOrderId": "RESORD907c659756fb",
        "orderCreatDate": "2025-04-17 13:16:07"
    },
    {
        "_id": "6800aaa71f1ca1ef95137343",
        "restaurantOrderId": "RESORDd01b213f13c4",
        "orderCreatDate": "2025-04-17 15:15:51"
    },
    {
        "_id": "6800e36e1f1ca1ef95137351",
        "restaurantOrderId": "RESORDb88c71676828",
        "orderCreatDate": "2025-04-17 19:18:06"
    },
    {
        "_id": "6801e8081f1ca1ef95137365",
        "restaurantOrderId": "RESORD7ef83f8ae584",
        "orderCreatDate": "2025-04-18 13:50:00"
    },
    {
        "_id": "680212d54f6af029d1f94d17",
        "restaurantOrderId": "RESORDf21e990cc447",
        "orderCreatDate": "2025-04-18 16:52:37"
    },
    {
        "_id": "680213f52ad63ccf0393bf82",
        "restaurantOrderId": "RESORD0183c359f18f",
        "orderCreatDate": "2025-04-18 16:57:25"
    },
    {
        "_id": "680216614f6af029d1f94d1b",
        "restaurantOrderId": "RESORDd61a392569bf",
        "orderCreatDate": "2025-04-18 17:07:45"
    },
    {
        "_id": "680221204f6af029d1f94d1c",
        "restaurantOrderId": "RESORD0e16823b581f",
        "orderCreatDate": "2025-04-18 17:53:36"
    },
    {
        "_id": "68024a044f6af029d1f94d23",
        "restaurantOrderId": "RESORD3db0bf5868e1",
        "orderCreatDate": "2025-04-18 20:48:04"
    },
    {
        "_id": "6802f0b24f6af029d1f94d27",
        "restaurantOrderId": "RESORD2aae7c502b4c",
        "orderCreatDate": "2025-04-19 08:39:14"
    },
    {
        "_id": "680317052ad63ccf0393bf97",
        "restaurantOrderId": "RESORD3caf68ba459a",
        "orderCreatDate": "2025-04-19 11:22:45"
    },
    {
        "_id": "68031ea42ad63ccf0393bf98",
        "restaurantOrderId": "RESORD868962d2b31a",
        "orderCreatDate": "2025-04-19 11:55:16"
    },
    {
        "_id": "68044e754f6af029d1f94d2d",
        "restaurantOrderId": "RESORDa44d6171b9f6",
        "orderCreatDate": "2025-04-20 09:31:33"
    },
    {
        "_id": "680513102ad63ccf0393bfb1",
        "restaurantOrderId": "RESORD929c67d5c84a",
        "orderCreatDate": "2025-04-20 23:30:24"
    },
    {
        "_id": "680514662ad63ccf0393bfb2",
        "restaurantOrderId": "RESORD118a7345a473",
        "orderCreatDate": "2025-04-20 23:36:06"
    }
]  # 此处粘贴你提供的订单列表

# 分组容器
grouped = defaultdict(list)

# 遍历并分组
for item in data:
    # 提取日期部分（假设格式为 'YYYY-MM-DD HH:mm:ss'）
    date_str = item["orderCreatDate"]
    date_only = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S").date().isoformat()
    grouped[date_only].append({
        "restaurantOrderId": item["restaurantOrderId"],
        "orderCreatDate": item["orderCreatDate"]
    })

# 输出结果（按日期排序）
for date in sorted(grouped):
    print(f"📅 {date} ({len(grouped[date])}条):")
    for entry in grouped[date]:
        print(f"  - {entry['restaurantOrderId']} @ {entry['orderCreatDate']}")

# {
#   "call_history": {
#     "$exists": true,
#     "$not": { "$size": 0 }
#   },
#   "$expr": {
#     "$eq": [
#       { "$arrayElemAt": ["$call_history.call_status", -1] },
#       "未接听"
#     ]
#   },
#   "restaurantLanguage": "ja",
#   "customerPhone": {
#     "$nin": [
#       "+86-17610051530", "17610051530",
#       "+86-17633328318", "17633328318",
#       "+86-18525518667", "18525518667",
#       "+86-15383433994", "15383433994",
#       "+86-18910480870", "18910480870",
#       "+86-18303339871", "18303339871",
#       "+86-16603129871", "16603129871",
#       "+86-15320206430", "15320206430",
#       "+86-15510096430", "15510096430",
#       "+86-18600068082", "18600068082",
#       "+86-13296582335", "13296582335"
#     ]
#   },
#   "orderCreatDate": {
#     "$gte": "2025-03-31T00:00:00" ,
#     "$lt":  "2025-04-22T00:00:00"
#   }
# }