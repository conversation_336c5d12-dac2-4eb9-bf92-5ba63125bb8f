import base64
import json
import os

import requests

def call_gpt4o_with_single_image(image_path: str, api_key: str, endpoint: str, filter_prompt: str) -> bool:
    """
    调用 GPT-4o 处理单张图片，返回是否符合筛选条件

    :param image_path: 需要筛选的图片路径
    :param api_key: Azure API密钥
    :param endpoint: API终结点
    :param filter_prompt: 筛选条件描述
    :return: 是否符合筛选条件（True/False）
    """
    # 读取图片并编码为 Base64
    with open(image_path, "rb") as f:
        base64_image = base64.b64encode(f.read()).decode("utf-8")

    # 构造 GPT-4o 消息
    messages = [{
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": f"{filter_prompt}"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{base64_image}",
                }
            }
        ]
    }]

    # 构造请求头
    headers = {
        "Content-Type": "application/json",
        "api-key": api_key
    }

    # 构造请求体
    payload = {
        "messages": messages,
        "temperature": 0,
        "max_tokens": 80,
        "top_p": 0.5
    }

    # 发送请求

    response = requests.post(
        endpoint,
        headers=headers,
        json=payload,
        timeout=30
    )
    response.raise_for_status()

    # 解析 GPT-4o 返回的内容
    result_text = response.json()['choices'][0]['message']['content'].strip()
    print(response.json())
    return result_text# 只要回答"是"或"yes"即符合筛选条件

# **使用示例**
if __name__ == "__main__":
    AZURE_API_KEY = "********************************"
    API_ENDPOINT = "https://133cn-westus3-new.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-04-01-preview"

    # 单张图片路径
    # image_file = "C://Users//hkz//Pictures//Saved Pictures//下载.png"
    # image_file = "D://chromedownload//t250317.jpg"
    #
    # # 发送请求
    # is_selected = call_gpt4o_with_single_image(
    #     image_path=image_file,
    #     api_key=AZURE_API_KEY,
    #     endpoint=API_ENDPOINT,
    #     filter_prompt="返回图片中涉及的餐厅名称, 不需要返回其他内容"
    # )

    directory = r"D:\chromedownload\t250317"

    for root, _, files in os.walk(directory):
        for file in files:
            print(os.path.join(root, file))
            # 发送请求
            is_selected = call_gpt4o_with_single_image(
                image_path=os.path.join(root, file),
                api_key=AZURE_API_KEY,
                endpoint=API_ENDPOINT,
                filter_prompt="返回图片中涉及的餐厅名称, 不需要返回其他内容"
            )
            print(is_selected)