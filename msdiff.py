from datetime import datetime

data = [
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:25:24.556",
        "streamFirstMsg": "2025-07-23T15:25:25.688",
        "ModelFlowServiceCallStart": "2025-07-23T15:25:22.728",
        "finished": "2025-07-23T15:25:29.288",
        "begin": "2025-07-23T15:25:22.689"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:31:20.941",
        "streamFirstMsg": "2025-07-23T15:31:22.236",
        "ModelFlowServiceCallStart": "2025-07-23T15:31:16.355",
        "finished": "2025-07-23T15:31:24.571",
        "begin": "2025-07-23T15:31:16.005"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:31:55.853",
        "streamFirstMsg": "2025-07-23T15:31:57.218",
        "ModelFlowServiceCallStart": "2025-07-23T15:31:53.809",
        "finished": "2025-07-23T15:32:01.845",
        "begin": "2025-07-23T15:31:53.73"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:32:20.873",
        "streamFirstMsg": "2025-07-23T15:32:22.179",
        "ModelFlowServiceCallStart": "2025-07-23T15:32:19.232",
        "finished": "2025-07-23T15:32:25.025",
        "begin": "2025-07-23T15:32:19.177"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:32:46.207",
        "streamFirstMsg": "2025-07-23T15:32:47.087",
        "ModelFlowServiceCallStart": "2025-07-23T15:32:44.352",
        "finished": "2025-07-23T15:32:49.328",
        "begin": "2025-07-23T15:32:44.305"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:34:34.279",
        "streamFirstMsg": "2025-07-23T15:34:35.259",
        "ModelFlowServiceCallStart": "2025-07-23T15:34:32.017",
        "finished": "2025-07-23T15:34:39.656",
        "begin": "2025-07-23T15:34:31.938"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:34:58.555",
        "streamFirstMsg": "2025-07-23T15:34:59.76",
        "ModelFlowServiceCallStart": "2025-07-23T15:34:56.708",
        "finished": "2025-07-23T15:35:01.203",
        "begin": "2025-07-23T15:34:56.665"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:35:20.679",
        "streamFirstMsg": "2025-07-23T15:35:21.46",
        "ModelFlowServiceCallStart": "2025-07-23T15:35:17.912",
        "finished": "2025-07-23T15:35:24.845",
        "begin": "2025-07-23T15:35:17.867"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:36:12.65",
        "streamFirstMsg": "2025-07-23T15:36:13.573",
        "ModelFlowServiceCallStart": "2025-07-23T15:36:10.61",
        "finished": "2025-07-23T15:36:16.406",
        "begin": "2025-07-23T15:36:10.556"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:39:07.949",
        "streamFirstMsg": "2025-07-23T15:39:09.011",
        "ModelFlowServiceCallStart": "2025-07-23T15:39:05.113",
        "finished": "2025-07-23T15:39:11.68",
        "begin": "2025-07-23T15:39:05.06"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:46:27.772",
        "streamFirstMsg": "2025-07-23T15:46:29.034",
        "ModelFlowServiceCallStart": "2025-07-23T15:46:25.466",
        "finished": "2025-07-23T15:46:35.21",
        "begin": "2025-07-23T15:46:25.089"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:46:47.178",
        "streamFirstMsg": "2025-07-23T15:46:48.02",
        "ModelFlowServiceCallStart": "2025-07-23T15:46:45.329",
        "finished": "2025-07-23T15:46:49.517",
        "begin": "2025-07-23T15:46:45.284"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:47:03.569",
        "streamFirstMsg": "2025-07-23T15:47:04.869",
        "ModelFlowServiceCallStart": "2025-07-23T15:47:01.718",
        "finished": "2025-07-23T15:47:06.498",
        "begin": "2025-07-23T15:47:01.658"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:47:16.844",
        "streamFirstMsg": "2025-07-23T15:47:18.207",
        "ModelFlowServiceCallStart": "2025-07-23T15:47:14.772",
        "finished": "2025-07-23T15:47:19.579",
        "begin": "2025-07-23T15:47:14.721"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:47:32.786",
        "streamFirstMsg": "2025-07-23T15:47:33.632",
        "ModelFlowServiceCallStart": "2025-07-23T15:47:30.315",
        "finished": "2025-07-23T15:47:36.062",
        "begin": "2025-07-23T15:47:30.262"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:51:35.309",
        "streamFirstMsg": "2025-07-23T15:51:36.309",
        "ModelFlowServiceCallStart": "2025-07-23T15:51:34.088",
        "finished": "2025-07-23T15:51:39.15",
        "begin": "2025-07-23T15:51:34.04"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:52:05.129",
        "streamFirstMsg": "2025-07-23T15:52:06.317",
        "ModelFlowServiceCallStart": "2025-07-23T15:52:02.182",
        "finished": "2025-07-23T15:52:07.802",
        "begin": "2025-07-23T15:52:02.131"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:52:23.015",
        "streamFirstMsg": "2025-07-23T15:52:24.028",
        "ModelFlowServiceCallStart": "2025-07-23T15:52:20.951",
        "finished": "2025-07-23T15:52:26.7",
        "begin": "2025-07-23T15:52:20.898"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:52:29.393",
        "streamFirstMsg": "2025-07-23T15:52:30.251",
        "ModelFlowServiceCallStart": "2025-07-23T15:52:28.157",
        "finished": "2025-07-23T15:52:31.862",
        "begin": "2025-07-23T15:52:28.107"
    },
    {
        "ModelFlowServiceCallFinished": "2025-07-23T15:53:42.375",
        "streamFirstMsg": "2025-07-23T15:53:43.219",
        "ModelFlowServiceCallStart": "2025-07-23T15:53:41.154",
        "finished": "2025-07-23T15:53:44.813",
        "begin": "2025-07-23T15:53:41.097"
    }
]

fmt = "%Y-%m-%dT%H:%M:%S.%f"

total_streamFirst_begin = 0
total_flow = 0
total_search = 0
count = 0

for row in data:
    try:
        t_stream = datetime.strptime(row["streamFirstMsg"], fmt)
        t_begin = datetime.strptime(row["begin"], fmt)
        t_call_start = datetime.strptime(row["ModelFlowServiceCallStart"], fmt)
        t_call_end = datetime.strptime(row["ModelFlowServiceCallFinished"], fmt)

        diff1 = (t_stream - t_begin).total_seconds() * 1000  # streamFirstMsg - begin
        diff2 = (t_call_end - t_call_start).total_seconds() * 1000  # workflow耗时
        diff3 = (t_stream - t_call_end).total_seconds() * 1000  # search延迟

        total_streamFirst_begin += diff1
        total_flow += diff2
        total_search += diff3
        count += 1
    except Exception as e:
        print(f"跳过一条数据，解析失败: {e}")

if count > 0:
    print(f"1. 平均 streamFirstMsg - begin: {total_streamFirst_begin / count:.2f} ms")
    print(f"2. 平均 ModelFlowServiceCallFinished - ModelFlowServiceCallStart: {total_flow / count:.2f} ms")
    print(f"3. 平均 streamFirstMsg - ModelFlowServiceCallFinished: {total_search / count:.2f} ms")
else:
    print("无有效数据")
