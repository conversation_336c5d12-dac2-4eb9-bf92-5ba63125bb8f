import asyncio
import time
from asyncio.windows_events import EventLoop


async def a():
    print("欢迎使用 a ！")
    # time.sleep(1)
    await asyncio.sleep(1)
    print("欢迎回到 a ！")

async def b():
    print("欢迎来到 b ！")
    # time.sleep(2)
    await asyncio.sleep(2)
    print("欢迎回到 b ！")

async def main():
    task1 = asyncio.create_task(a())
    task2 = asyncio.create_task(b())
    print("准备开始")
    await task1
    print("task1 结束")
    await task2
    print("task2 结束")

if __name__ == "__main__":
    start = time.perf_counter()

    asyncio.run(main())

    print('花费 {} s'.format(time.perf_counter() - start))


# async def a():
#     await asyncio.sleep(1)
#
# asyncio.run(a())
# loop = EventLoop()
# loop