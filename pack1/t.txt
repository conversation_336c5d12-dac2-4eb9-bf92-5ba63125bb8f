
    """订餐卡样式order_card_type"""
    order_card_type: Optional[Union[str, Any]] = ""
    """通话记录类型call_record_type"""
    call_record_type: Optional[Union[str, Any]] = ""
    """人工拨打ID humanBookId"""
    humanBookId: ""
    """订餐日期"""
    diningDate: str
    """订餐时间"""
    diningTime: str
    """订餐位置"""
    diningLocation: str
    """订餐人数"""
    numberOfPeople: str
    """numberOfChildren"""
    numberOfChildren: str
    """订单备注"""
    specialReminder: str
    """姓氏样式"""
    name_style: Optional[Union[str, Any]]
    """人数样式"""
    people_style: Optional[Union[str, Any]]
    """预订人手机号"""
    bookerPhone: str
    """预订人姓名"""
    bookerName: str
    """firstName"""
    firstName: str
    """lastName"""
    lastName: str
    """customerSex"""
    customerSex: str
    """diningVoucherUrl"""
    diningVoucherUrl: str
    """套餐名称mealSetName"""
    mealSetName = Optional[Union[str, Any]] = ""
    """预约编号reservationNo"""
    reservationNo = Optional[Union[str, Any]] = ""
    """预约邮箱reservationEmail"""
    reservationEmail = Optional[Union[str, Any]] = ""