import datetime
import json
from datetime import time

def get_biz_hours(target_date: datetime.date, holidays: list[datetime.date]) -> list[tuple[time, time]]:
    from datetime import time

    # Define business hours
    weekday_hours = (time(19, 0), time(3, 0))  # 19:00 - 03:00
    closed_days = []

    # Check if the target date is a holiday
    if target_date in holidays:
        return closed_days

    # Check if the target date is a Sunday
    if target_date.weekday() == 6:  # Sunday
        # Check if it's a Sunday that is part of a three-day weekend
        if any((target_date - datetime.timedelta(days=i)).weekday() == 5 for i in range(1, 3)):
            return [weekday_hours]
        else:
            return closed_days

    # For Monday to Saturday
    if target_date.weekday() in range(0, 6):  # Monday to Saturday
        return [weekday_hours]

    return closed_days

if __name__ == '__main__':
    print(get_biz_hours(datetime.date(2025,3,18), []))