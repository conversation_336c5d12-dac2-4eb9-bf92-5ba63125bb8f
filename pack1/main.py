import os
import argparse

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from api import main_router
from datetime import datetime, time, timedelta
import datetime


def get_opening_hours(target_date: datetime.date, holidays: list[datetime.date]) -> list[tuple[time, time]]:
    # Define constants
    weekday_open_hours = [(time(11, 30), time(14, 0)), (time(17, 0), time(5, 0))]
    friday_pre_holiday_hours = [(time(17, 0), time(5, 0))]
    saturday_holiday_hours = [(time(11, 30), time(15, 0)), (time(15, 0), time(5, 0))]
    sunday_hours = [(time(11, 30), time(15, 0)), (time(15, 0), time(21, 0))]
    post_holiday_hours = [(time(17, 0), time(5, 0))]

    # Determine the weekday and its context
    weekday = target_date.weekday()

    # Calculate neighboring days
    previous_day = target_date - timedelta(days=1)
    next_day = target_date + timedelta(days=1)

    # Determine if today or neighboring days are holidays
    is_today_holiday = target_date in holidays
    is_yesterday_holiday = previous_day in holidays
    is_tomorrow_holiday = next_day in holidays

    # Set business hours based on conditions
    if is_today_holiday:
        business_hours = saturday_holiday_hours if weekday == 5 else friday_pre_holiday_hours
    elif is_tomorrow_holiday:
        business_hours = friday_pre_holiday_hours if weekday == 4 else weekday_open_hours
    elif is_yesterday_holiday:
        business_hours = post_holiday_hours
    elif weekday == 0 or weekday == 1 or weekday == 2 or weekday == 3:
        business_hours = weekday_open_hours
    elif weekday == 4:
        business_hours = friday_pre_holiday_hours
    elif weekday == 5:
        business_hours = saturday_holiday_hours
    elif weekday == 6:
        business_hours = sunday_hours

    # Adjust time format for cross-midnight operations
    adjusted_hours = []
    for start, end in business_hours:
        if end < start:
            adjusted_end = time(end.hour + 24, end.minute)
        else:
            adjusted_end = end
        adjusted_hours.append((start, adjusted_end))

    return adjusted_hours

if __name__ == '__main__':
    get_opening_hours(datetime.date(2025,3,18), [datetime.date(2025, 3, 28)])