from datetime import datetime

import aiohttp
import asyncio

async def wrong_timeout_demo():
    print("开始请求（错误方式 timeout=10）")
    print(datetime.now())
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://10.255.255.1", timeout=2) as resp:
                print(await resp.text())
    except Exception as e:
        print(f"异常捕获: {e}")
    print(datetime.now())

asyncio.run(wrong_timeout_demo())
