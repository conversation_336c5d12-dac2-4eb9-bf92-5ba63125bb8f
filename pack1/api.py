from fastapi import APIRouter

main_router = APIRouter()


# 测试接口
@main_router.get("/")
async def read_root():
    extract_info_prompt = """你需要完成下面两个任务来帮助用户提取信息：
任务1: 识别当前用户的意图：
- 用户意图(selected_intent)定义: 值为1或2
- 可选意图： 
    1: 推荐餐厅: 当聊天历史中没有提及具体的餐厅名称时，需要为用户推荐餐厅时为此意图。 
    2: 收集定餐信息: 当聊天历史中提及了具体的餐厅名称时，需要收集预定餐厅信息时为此意图。

任务2: 从聊天记录中提取信息：
注意：
- 现在的日期是: 【CurrentDate】, 现在的时间为： 【CurrentTime】。就餐日期和就餐时间需要根据现在的日期和时间推算, 如: 今天(【Today】)、明天(【Tomorrow】)、后天(【AfterTomorrow】)等。
- 按照不同意图的输出要求提取信息，不要提取多余的信息。
- 意图1需要提取的信息字段： selected_intent, if_search_restaurant, search_restaurant_query, if_search_strategy, search_strategy_query
- 意图2需要提取的信息字段： selected_intent, if_search_restaurant, search_restaurant_query, restaurantCode, restaurantName, restaurantAddress, restaurantPhone, restaurantPhone2, restaurantPhone3, diningDate, diningTime, diningLocation, numberOfPeople, specialRequest, customerName, lastName, firstName, customerSex, customerEmail, acceptChangeLocation, acceptChangeTime

关键字段说明：
- 是否需要搜索餐厅(if_search_restaurant): 值为: yes/no 是否需要搜索餐厅信息回答用户的问题(如果用户提供的餐厅名称等信息在历史记录的餐厅搜索信息中没有找到，则需要搜索餐厅值(yes), 如果用户更改了餐厅信息， 则必须搜索餐厅值(yes), 否则不需要再次搜索餐厅(no))
- 搜索关键词(search_restaurant_query): (如果需要搜索餐厅时(if_search_restaurant=yes) 的时候必须提取, 否则为空)(定义：聊天历史中用户提到的包含餐厅名称、餐厅地址、餐厅电话等有助于搜索餐厅信息的关键词。)
- 是否搜索攻略(if_search_strategy): 值为: yes/no (是否需要搜索餐厅攻略辅助回答用户的问题)
- 搜索攻略关键词(search_strategy_query): (if_search_strategy为 yes 时必填,为 no 时不填)(用于在美食攻略数据库中搜索餐厅攻略的关键词)
- 用户选择餐厅后请从搜索到的餐厅信息中提取餐厅的所有信息，包括餐厅编号（restaurantCode）、餐厅名称（restaurantName）、餐厅地址（restaurantAddress）、餐厅电话（restaurantPhone、restaurantPhone2、restaurantPhone3）、。
- 聊天记录中没有提及的内容请设置为空
"""

    return extract_info_prompt
# async def read_root():
#
#     return {"message": "hello world!"}
