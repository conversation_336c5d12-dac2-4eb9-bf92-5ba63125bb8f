import datetime
import requests
from loguru import logger


# 查询考勤记录
def querylistByUser(recordDateStr, cookies):
    url = "https://ssomanager.133.cn/manager/attendance/querylistByUser"
    data = {
        "recordDateStr": recordDateStr
    }
    response = requests.post(url, cookies=cookies, data=data)
    return response.json()


# 查询考勤记录并统计
def query_run(cookies):
    now_time = datetime.datetime.now()
    last_month_day = (now_time - datetime.timedelta(days=30)).strftime("%Y-%m-28")
    last_month = (now_time - datetime.timedelta(days=30)).strftime("%Y-%m")
    today = now_time.strftime("%Y-%m")
    responses = []
    for month in [last_month, today]:
        print("查询{}月考勤记录".format(month))
        month_str = month.split("-")[-1]
        year = month.split("-")[0]
        if "0" in str(month_str)[0]:
            month_str = month_str.replace("0", "")
        query_month = year + "-" + month_str
        responses.append(querylistByUser(query_month, cookies))
    datas = []
    names = []
    for month_data in responses:
        names.append(month_data["data"]["name"])
        for key in range(1, 32):
            key = str(key)
            if key in month_data["data"]:
                if month_data["data"][key]["recordDate"]:
                    if datetime.datetime.strptime(month_data["data"][key]["recordDate"],
                                                  "%Y-%m-%d") < datetime.datetime.strptime(last_month_day, "%Y-%m-%d"):
                        continue
                datas.append(month_data["data"][key])

    work_day = 0
    workTimes = 0
    for data in datas:
        workTime = data["workTime"]
        if workTime < 0:
            workTime = 480
        if not workTime:
            continue
        work_day += 1
        workTimes += workTime
        logger.info(
            f"recordDate:{data['recordDate']}, startTime:{data['startTime']}, endTime:{data['endTime']}, workTime:{data['workTime']}")
    worker_name = ""
    for idx, name in enumerate(names):
        if idx == 0:
            worker_name = name
        else:
            add_str = "~" + name.split(" ")[1]
            worker_name += add_str

    logger.info("姓名:{}".format(worker_name))
    logger.info("总工作时长:{}".format(round(workTimes / 60, 2)))
    logger.info("平均工作时长:{}".format(round(workTimes / 60 / work_day, 2)))
    logger.info("工作天数:{}".format(work_day))
    logger.info("超出工作时长(min):{}".format(workTimes - (work_day * 60 * 8)))
    return {"姓名": worker_name,
            "总工作时长": round(workTimes / 60, 2),
            "平均工作时长": round(workTimes / 60 / work_day, 2),
            "工作天数": work_day,
            "超出工作时长(min)": workTimes - (work_day * 60 * 8)}


if __name__ == '__main__':
    cookies = {'JSESSIONID': '3259635DC19D2E7C3A4072C2561EC281'}
    query_run(cookies)