# import uuid
# import requests
#
# def tripNowLiao(user_input: str):
#     url = "http://***********:14414/?cmd=aichat"
#     domain = "dings"
#     data = {
#         "sessionId": uuid.uuid4(),
#         "domain": domain,
#         "msg": user_input,
#     }
#     response = requests.post(url, data=data)
#     try:
#         last_content = response.json()["data"]["userHistory"][-1]["content"]
#     except (KeyError, IndexError, TypeError):
#         last_content = None
#     print(last_content)
#
# tripNowLiao("北京餐厅推荐")

import requests

def getCityObjFromName(cityStr: str):
    def fetch_city_list(country_code):
        url = f"https://dings.133.cn/online/hot_city/get_city_list?language=zh&country={country_code}"
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                return response.json().get("data", {})
            else:
                print(f"请求失败: {url}, 状态码: {response.status_code}")
        except Exception as e:
            print(f"请求异常: {e}")
        return {}

    def search_city(data):
        for key in data:
            for item in data[key]:
                for city_name, city_obj in item.items():
                    if city_name == cityStr:
                        return city_obj
        return None

    # 先查中国
    cn_data = fetch_city_list("CN")
    city_obj = search_city(cn_data)
    if city_obj:
        return city_obj

    # 再查日本
    jp_data = fetch_city_list("JP")
    city_obj = search_city(jp_data)
    if city_obj:
        return city_obj

    # 没找到
    return None

print(getCityObjFromName("东京"))