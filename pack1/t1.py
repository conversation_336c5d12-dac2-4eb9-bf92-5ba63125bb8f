#
# async def format_rest_info_single(rest: dict, idx: int, language_code: str) -> Tuple[Dict, str]:
#     rest_info = {}
#     try:
#         if "_source" in rest:
#             rest = rest["_source"]
#         # 处理餐厅信息
#         restaurantCode = rest.get("store_id", "")
#         rest_info["restaurantCode"] = restaurantCode
#         # 处理dings_id
#         dings_id = rest.get("dings_id", "")
#         rest_info["dings_id"] = str(dings_id)
#         # 处理餐厅名称
#         restaurantName = rest.get("name", "")
#         restaurantAnotherName = rest.get("another_name", "")
#         rest_info["restaurantName"] = restaurantAnotherName
#         rest_info["restaurantAnotherName"] = restaurantName
#         # 处理餐厅地址
#         restaurantAddress = rest.get("address_str", "")
#         rest_info["restaurantAddress"] = restaurantAddress
#         # 处理餐厅经纬度
#         restaurantLocation = rest.get("location", {})
#         rest_info["restaurantLocation"] = restaurantLocation
#         # 餐厅数据来源
#         query_method = rest.get("query_method", "")
#         restDataSource = rest_data_source_map.get(query_method, {})
#         if not restDataSource:
#             restDataSource = rest_data_source_map.get("default", {})
#         rest_info["restDataSource"] = restDataSource
#         # 处理餐厅语言
#         country_code = rest.get("country_code", "CN")
#         restaurantLanguage = country_lang_dict.get(country_code, "zh")
#         rest_info["countryCode"] = country_code
#         rest_info["restaurantLanguage"] = restaurantLanguage
#         # 处理推荐菜字段
#         recommended_dishes = rest.get("recommended_dishes", [])
#         if recommended_dishes and isinstance(recommended_dishes, list):
#             if not isinstance(recommended_dishes[0], dict):
#                 recommended_dishes = []
#             else:
#                 recommended_dishes = recommended_dishes[:5]
#         rest_info["recommendedDishes"] = recommended_dishes
#
#         # 处理餐厅类别
#         if country_code == "CN":
#             tags = rest.get("tags", [])
#             special_services = rest.get("special_services", [])
#             facilities = rest.get("facilities", [])
#         else:
#             tags = rest.get("ai_tags", [])
#             special_services = rest.get("special_services_tags", [])
#             facilities = rest.get("facilities_tags", [])
#         tags = format_label(tags)
#         facilities = format_label(facilities)
#         special_services = format_label(special_services)
#         rest_category = get_rest_category(tags, facilities, special_services, need_num=3)
#         rest_info["restCategory"] = rest_category
#         rest_info["specialServices"] = special_services
#         rest_info["facilities"] = facilities
#
#         # 处理餐厅是否含有tabelog日历
#         tabelog_contracted_restaurant = rest.get("tabelog_contracted_restaurant", "")
#         rest_info["tabelogContractedRestaurant"] = str(tabelog_contracted_restaurant)
#         # 处理价格
#         average_price = rest.get("average_price", "")
#         average_price_formatted = format_average_price(average_price, country_code)
#         rest_info["averagePrice"] = average_price_formatted
#         # 处理评分
#         rating = rest.get("rating", "")
#         # 将评分统一成数值
#         rating = parse_rating(rating)
#
#         rest_info["rating"] = rating
#         # 处理距离
#         distance_in_meters = rest.get("distance_in_meters", "")
#         distance_in_meters = parse_distance(distance_in_meters)
#         if not distance_in_meters:
#             distance_formatted = ""
#         elif distance_in_meters < 1000:
#             distance_formatted = f"{distance_in_meters:.1f}m"
#         elif distance_in_meters < 100000:
#             distance_formatted = f"{distance_in_meters / 1000:.1f}km"
#         else:
#             distance_formatted = ""
#         rest_info["distance"] = distance_formatted
#         # 处理餐厅描述
#         description = rest.get("description", "")
#         rest_info["description"] = description
#
#         # 处理餐厅评价
#         reviews = rest.get("reviews", [])
#         reviews_contents = ""
#         if reviews and isinstance(reviews, list):
#             for idx, review in enumerate(reviews[:5]):
#                 reviews_content = review.get("reviews_content", "")
#                 if reviews_content:
#                     reviews_contents += f"{id x +1}. {reviews_content} \t\t"
#         restaurantDescription = ""
#         if rating:
#             restaurantDescription += f"Rating: {rating} \t\t"
#         if average_price_formatted:
#             restaurantDescription += f"Average Price: {average_price_formatted} \t\t"
#         if recommended_dishes and isinstance(recommended_dishes[0], dict):
#             dishes_names = [dish.get("dish_name", "") for dish in recommended_dishes]
#             restaurantDescription += f"Recommended Dishes: {', '.join(dishes_names)} \t\t"
#         if tags:
#             restaurantDescription += f"Tags: {', '.join(tags)} \t\t"
#         if special_services:
#             restaurantDescription += f"Special Services: {', '.join(special_services[:5])} \t\t"
#         if facilities:
#             restaurantDescription += f"Facilities: {', '.join(facilities[:5])} \t\t"
#         if description:
#             restaurantDescription += f"{description} \t\t"
#         if reviews_contents:
#             restaurantDescription += f"Reviews: {reviews_contents} \t\t"
#         rest_info["restaurantDescription"] = restaurantDescription.strip()
#         rest_info["restaurantWebsite"] = rest.get("url", "")
#
#         # 处理餐厅图片
#         default_picture = "http://************/basic/files/bed5327b5fce2a59cccf9a291af40ca7.png"
#         cover_image = rest.get("cover_image", "") # 封面图
#         phones_1 = rest.get("photos", [])[0] if rest.get("photos", []) else default_picture
#         restaurantPicture = cover_image if cover_image else phones_1
#         restaurantPicture = restaurantPicture.replace("http://", "https://")
#         rest_info["restaurantPicture"] = restaurantPicture
#         restaurantPhotos = rest.get("photos", [default_picture])
#         restaurantPhotos = [photo.replace("http://", "https://") for photo in restaurantPhotos[:5]]
#         rest_info["restaurantPhotos"] = restaurantPhotos
#
#         # 处理餐厅电话号码
#         phone_number = rest.get("phone_number", "")
#         phone_nums = handle_rest_phone(phone_number, country_code)
#         rest_info.update(phone_nums)
#
#         # 处理标准化营业时间
#         businessHours = rest.get("default_business_hours", [])
#         if businessHours and isinstance(businessHours, list):
#             businessHours = handle_dial_hours_exception(businessHours)
#             # 标准化营业时间字符串
#             businessHoursStr = format_business_hours_str(businessHours, language_code)
#             logger.debug(f"###标准化营业时间###: businessHoursStr: {businessHoursStr}")
#         else:
#             businessHoursStr = rest.get("opening_hours", "")
#             if not isinstance(businessHoursStr, str):
#                 businessHoursStr = ""
#                 businessHours = []
#             else:
#                 logger.debug \
#                     (f"###无标准化营业时间###: dings_id: {dings_id}, restaurantName: {restaurantName}, businessHours: {businessHours}, businessHoursStr: {businessHoursStr}")
#                 businessHours = await format_rest_business_hours(businessHoursStr)
#                 businessHoursStr = format_business_hours_str(businessHours, language_code)
#         rest_info["businessHoursStr"] = businessHoursStr
#         rest_info["businessHours"] = businessHours
#         # 处理拨打时间
#         dialHours = rest.get("dial_time", [])
#         if dialHours and isinstance(dialHours, list):
#             dialHours = handle_dial_hours_exception(dialHours)
#             # 标准化拨打时间字符串
#             dialHoursStr = format_business_hours_str(dialHours, language_code)
#         elif businessHours:
#             dialHours = businessHours
#             dialHoursStr = format_business_hours_str(dialHours, language_code)
#         else:
#             dialHoursStr = ""
#         rest_info["dialHours"] = dialHours
#         rest_info["dialHoursStr"] = dialHoursStr
#
#         # 是否含有tabelog日历
#         tabelog_contracted_restaurant = rest.get("tabelog_contracted_restaurant", 0)
#         rest_info["tabelog_contracted_restaurant"] = tabelog_contracted_restaurant
#
#         # 餐厅是否支持预订
#         rest_info["bookingStatus"] = rest.get("booking_status", "")
#         # 是否需要定套餐
#         rest_info["bookingSetMeals"] = rest.get("booking_set_meals", "")
#         # 营业状态
#         rest_info["businessStatus"] = rest.get("status", "")
#         # 预定类型
#         rest_info["bookingType"] = rest.get("booking_type", [])
#         # 预定方式
#         rest_info["bookingMathod"] = rest.get("booking_mathod", "")
#         # 处理支持预订和按钮
#         book_available, button_info = get_book_button_info(rest, language_code, phone_nums)
#         # 添加支持预订的布尔值
#         rest_info["bookingAvailable"] = book_available
#         rest_info["button_info"] = button_info
#         # 交通方式
#         transportation = rest.get("transportation", "")
#         rest_info["transportation"] = transportation
#
#         # 处理餐厅介绍
#         rest_intro_list = language_fixed_dict.get("rest_intro_list", {}).get(language_code, [])
#         if not description:
#             rest_intro = random.choice(rest_intro_list)
#             restaurantIntroduce = rest_intro.replace("【RestaurantName】", restaurantName)
#             rest_info["restaurantIntroduce"] = restaurantIntroduce
#         else:
#             rest_info["restaurantIntroduce"] = description
#
#         return rest_info, restaurantName
#     except Exception as e:
#         logger.error(f"Error processing restaurant {idx}: {e}")
#         return None, None

# orders = [{'restaurantOrderId': 'RESORDd20dc404d1f1','dialog_id': '123'}]
#
# filtered_orders = [
#         {
#             "restaurantOrderId": order.get("restaurantOrderId"),
#             "dialog_id": order.get("dialog_id")
#         }
#         for order in orders
#         if "restaurantOrderId" in order and "dialog_id" in order
#     ]
#
# print(filtered_orders)

# 查找合适的就餐时间范围
# from datetime import datetime, timedelta, time
# def find_suitable_time(business_hours):
#
#
# 	now = datetime.now()
#
# 	# 检查今天、明天和后天的日期
# 	for day_offset in range(1, 3):
# 		check_date = now + timedelta(days=day_offset)
# 		check_weekday = (check_date.weekday() + 1) % 7  # 0变为周日, 1-6变为周一到周六
#
# 		for schedule in business_hours:
# 			days = schedule['days']
# 			start_time = schedule['start_time']
# 			end_time = schedule['end_time']
# 			cross_day = schedule['cross_day']
#
# 			if check_weekday in days:
# 				start_hour, start_minute = map(int, start_time.split(':'))
# 				start_datetime = check_date.replace(hour=start_hour, minute=start_minute, second=0, microsecond=0)
#
# 				if end_time == "24:00":
# 					end_hour, end_minute = 23, 59  # 24:00改为23:59
# 				else:
# 					end_hour, end_minute = map(int, end_time.split(':'))
#
# 				end_datetime = check_date.replace(hour=end_hour, minute=end_minute, second=0, microsecond=0)
# 				if cross_day:
# 					end_datetime += timedelta(days=1)
#
# 				suitable_start_time = max(check_date.replace(hour=18, minute=0, second=0, microsecond=0),
# 				                          start_datetime)
# 				suitable_end_time = end_datetime
#
# 				# 如果营业结束时间为23:59，适当处理
# 				if end_time == "24:00" and suitable_end_time.minute == 59:
# 					suitable_end_time = suitable_end_time.replace(minute=0) + timedelta(hours=1)
#
# 				if suitable_start_time <= suitable_end_time:
# 					return suitable_start_time.strftime("%Y-%m-%d %H:%M"), suitable_end_time.strftime("%Y-%m-%d %H:%M")
#
# 	return (None, None)
#
# res = find_suitable_time([{'days': [1, 2, 3, 4, 5, 6, 0], 'start_time': '05:00', 'end_time': '13:00', 'cross_day': False}])
# print(res)

import json
import os, sys
import asyncio
import aiohttp
import requests
from loguru import logger

# async def j_refactor_orderPreCommit(req: dict):
#     url = f"https://dings.133.cn/online/statistic_t/order/orderPreCommit"
#     async with aiohttp.ClientSession() as session:
#         async with session.post(url, data=req, timeout=10) as response:
#             response.raise_for_status()
#             response_json = await response.json()
#             logger.info(f"[oms4j_r]orderPreCommit: {response_json}")
#             return response_json
#
# async def method1():
#     d = {
#             "orderStatus": 0,
#             "restaurantDingsId": "454005",
#             "restaurantCode": "fukuoka_A4001_A400103_40051308_",
#             "restaurantName": "すみ劇場 むさし坐",
#             "restaurantAddress": "福岡県, 福岡市中央区, 渡辺通, 5-5-12",
#             "restaurantPhone": "00815055977133",
#             "restaurantPhone2": "",
#             "restaurantPhone3": "",
#             "restaurantWebsite": "",
#             "restaurantPicture": "https://tblg.k-img.com/resize/660x370c/restaurant/images/Rvw/112751/112751304.jpg?token=28e4a8f&api=v2",
#             "restaurantLanguage": "ja",
#             "businessHoursStr": "周一~周五 16:00 - 23:59，周六 15:00 - 23:59，周日 15:00 - 次日 23:00",
#             "businessHours": [
#                 {
#                     "start_time": "16:00",
#                     "cross_day": False,
#                     "end_time": "23:59",
#                     "days": [
#                         1,
#                         2,
#                         3,
#                         4,
#                         5
#                     ],
#                     "late_operation_end_time": "00:00"
#                 },
#                 {
#                     "start_time": "15:00",
#                     "cross_day": False,
#                     "end_time": "23:59",
#                     "days": [
#                         6
#                     ],
#                     "late_operation_end_time": "00:00"
#                 },
#                 {
#                     "start_time": "15:00",
#                     "cross_day": True,
#                     "end_time": "23:00",
#                     "days": [
#                         0
#                     ],
#                     "late_operation_end_time": "23:00"
#                 }
#             ],
#             "dialHoursStr": "周一~周五 16:00 - 23:59，周六 15:00 - 23:59，周日 15:00 - 次日 23:00",
#             "dialHours": [
#                 {
#                     "start_time": "16:00",
#                     "cross_day": False,
#                     "end_time": "23:59",
#                     "days": [
#                         1,
#                         2,
#                         3,
#                         4,
#                         5
#                     ],
#                     "late_operation_end_time": "00:00"
#                 },
#                 {
#                     "start_time": "15:00",
#                     "cross_day": False,
#                     "end_time": "23:59",
#                     "days": [
#                         6
#                     ],
#                     "late_operation_end_time": "00:00"
#                 },
#                 {
#                     "start_time": "15:00",
#                     "cross_day": True,
#                     "end_time": "23:00",
#                     "days": [
#                         0
#                     ],
#                     "late_operation_end_time": "23:00"
#                 }
#             ],
#             "nearestOpeningTime": "",
#             "nearestCancelTime": "",
#             "if_end_call": False,
#             "diningDate": "2025-05-02",
#             "diningTime": "18:30",
#             "diningLocation": "テーブル席",
#             "diningMealSet": "",
#             "diningMealId": "",
#             "numberOfPeople": "1",
#             "numberOfChildren": "0",
#             "specialRequest": "",
#             "customerPhone": "+86-13423894289",
#             "countryCode": "JP",
#             "customerName": "WinnyNiu",
#             "firstName": "ミン",
#             "lastName": "Niu",
#             "customerEmail": "",
#             "customerAddress": "",
#             "customerSex": "",
#             "bookerName": "カン",
#             "bookerPhone": "08053226002",
#             "language": {
#                 "Code": "zh",
#                 "Chinese": "中文",
#                 "English": "Chinese",
#                 "Native": "中文"
#             },
#             "acceptChangeLocation": True,
#             "acceptChangeTime": False,
#             "currencyCode": "CNY",
#             "external_userid": "",
#             "open_kfid": "",
#             "voiceId": "",
#             "realCallPhone": "",
#             "special_request_json": {},
#             "server_mode": "online",
#             "user_id": "ueac36a51164245e3bb092738a818528b",
#             "dialog_id": "de3fff47b515649dbb96dc138f3c8735e",
#             "channelName": "Dings iOS",
#             "channelCode": "dings_ios"
#         }
#
#     v = await j_refactor_orderPreCommit(d)
#     print(v)
#
# if __name__ == '__main__':
#     asyncio.run(method1())

# async def j_refactor_getCurrCancelType(orderId: str) -> dict:
#     url = f"https://dingstest.133.cn/test/order_manage4j/order/getCurrCancelType?orderId={orderId}"
#     async with aiohttp.ClientSession() as session:
#         async with session.get(url, timeout=10) as response:
#             response.raise_for_status()
#             response_json = await response.json()
#             logger.info(f"[oms4j_r]cancel: {response_json}")
#             return response_json

# async def j_refactor_cancel(req: dict):
#     url = f"http://localhost:8080/order/cancel"
#     async with aiohttp.ClientSession() as session:
#         async with session.post(url, data=json.dumps(req), timeout=10) as response:
#             response.raise_for_status()
#             return True
# async def j_refactor_paySuccess(req: dict):
#     url = f"https://dingstest.133.cn/test/order_manage4j/order/paySuccess"
#     # url = f"http://localhost:8080/order/paySuccess"
#     headers = {'Content-Type': 'application/json'}
#     print(req)
#     async with aiohttp.ClientSession() as session:
#         async with session.post(url, headers=headers, data=json.dumps(req), timeout=10) as response:
#             response.raise_for_status()
#             return True
# async def m():
#     data = {'paymentInfo': {'serviceFee': 80, 'serviceFeeDiscount': 0.99, 'depositFee': 0.01, 'deposit': 0.005, 'totalFee': 1.0, 'currencyCode': 'CNY', 'currencySymbol': '¥', 'person_count': 2, 'order_title': '2人预定费', 'pay_time': '2025-04-11T18:35:05'}}
#     order_id = "RESORD116873386226"
#     res: bool = await j_refactor_paySuccess({"orderId": order_id, "payType": "HUO_LI", "amt": data.get("paymentInfo", {}).get("totalFee", 0), "payTime": data.get("paymentInfo", {}).get("pay_time", 0), "paySrc": str(data)})
#     print(res)

# def j_refactor_latestCallTask(orderId: str) -> dict:
#     url = f"http://localhost:8080/phoneOrder/latestCallTask?orderId={orderId}"
#     headers = {'Content-Type': 'application/json'}
#     response = requests.request("GET", url, headers=headers, timeout=30)
#     response.raise_for_status()
#     try:
#         response_json = response.json()
#     except ValueError:
#         # 响应体不是合法 JSON，可能是空或 plain text
#         logger.warning("[oms4j_r]latestCallTask 响应为空或非法 JSON")
#         return {}
#
#     logger.info(f"[oms4j_r]latestCallTask,{response_json}")
#     return response_json or {}

queryObj = {'max_price': '12000', 'min_price': '8000', 'city': '东京', 'recommend_status': '0', 'min_rating': '', 'type': '', 'landmark': '银座', 'intent': '查询', 'max_rating': '', 'restaurant_name': '', 'tags': []}
tripNowProcessResult = {}
try:
    trip_now_query = {"page_num": 1, "page_size": 3}
    new_extendQuery = {}
    # if queryObj.get("city") and queryObj.get("city") != "":
    #     cityObj = getCityObjFromName(queryObj["city"])
    #     if cityObj and cityObj.get("city_id") != location.get("city_id", ""):
    #         #切换城市
    #         new_extendQuery.update({"country": cityObj.get("country"),
    #                            "city_id": cityObj.get("city_id")})
    #         new_extendQuery.pop("latitude", None)
    #         new_extendQuery.pop("longitude", None)
    #         cityStr = queryObj.get("city")

    if str(queryObj.get("recommend_status")) == "1":
        new_extendQuery.update({"recommend_status": 1})
    elif queryObj.get("restaurant_name") and queryObj.get("restaurant_name") != "":
        trip_now_query.update({"query": queryObj.get("restaurant_name")})
    if queryObj.get("type") and queryObj.get("type") != "":
        new_extendQuery.update({"type": queryObj.get("type")})
    raw_min_price = queryObj.get("min_price")
    mul20: bool = False
    if raw_min_price not in (None, ""):
        minPrice = int(raw_min_price)
        if "JP" == "JP" and minPrice < 500:
            minPrice *= 20
            mul20 = True
        new_extendQuery.update({"min_price": minPrice})
    raw_max_price = queryObj.get("max_price")
    if raw_max_price not in (None, ""):
        maxPrice = int(raw_max_price)
        if "JP" == "JP" and (maxPrice < 500 or mul20):
            maxPrice *= 20
        new_extendQuery.update({"max_price": maxPrice})

    if queryObj.get("min_rating") and queryObj.get("min_rating") != "":
        new_extendQuery.update({"min_rating": queryObj.get("min_rating")})
    if queryObj.get("max_rating") and queryObj.get("max_rating") != "":
        new_extendQuery.update({"max_rating": queryObj.get("max_rating")})
    if queryObj.get("tags"):
        new_extendQuery.update({"tags": queryObj.get("tags")})
    if queryObj.get("landmark"):
        new_extendQuery.update({"landmark": queryObj.get("landmark")})
        # s = locationStr2Geo(queryObj.get("landmark"), cityStr)
        # logger.info(f"location2Gep {s}")
        # if s:
        #     lon, lat = s.split(",")
        #     if lon and lat:
        #         new_extendQuery.update({
        #             "latitude": lat,
        #             "longitude": lon
        #         })

    trip_now_query.update({"extendQuery": new_extendQuery})
    extracted_rest_data = {"selected_intent": 1, "if_search_strategy": False,"if_search_restaurant": True,
                           "search_restaurant_query": '',"input_type": '', "search_restaurant_type": "trip_now",
                           "trip_now_query": trip_now_query}
    tripNowProcessResult.update({"tripNowQuery": trip_now_query})
except Exception as e:
    print(e)
    logger.error(f"tripNow liao处理异常")
    #原逻辑
    extracted_rest_data = {"selected_intent": 1, "if_search_strategy": False,"if_search_restaurant": True, "search_restaurant_query": 'user_message',"input_type": 'input_type'}
    tripNowProcessResult.update({"tripNowSuccess": False, "err": e})


# if __name__ == '__main__':
#     print(j_refactor_latestCallTask("RESORD116873385226"))
#     # asyncio.run(j_refactor_latestCallTask("!!"))