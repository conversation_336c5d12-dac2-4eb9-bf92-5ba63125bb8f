# ,"月・火・水・木・金, 06:30 - 09:00, 10:30 - 13:30, 土, 07:00 - 09:00, 11:00 - 13:00, 日・祝日, 定休日, ■ 営業時間, 1F, [月～金], 6:30～9:00, 10:30～13:30, [土], 7:00～9:00, 11:00～13:00, 2F, [月～金], 11:00～13:30, ■ 定休日, 年末年始, 営業時間・定休日は変更となる場合がございますので、ご来店前に店舗にご確認ください。","月・火・金・祝前日, 10:00 - 21:00, 水, 07:00 - 23:00, 土・日・祝日, 09:00 - 21:00, 木, 定休日, お粥・手作り点心はもちろん！麺類もご飯類もセットメニューもコース料理も！本店のメニューと同様にご用意。上海園独自のメニューもございます！宴会のご予約も喜んで承ります。皆様のご来店をお待ちしております。","火・水・木・金・土・日, 11:30 - 15:00, L.O. 14:00, 17:00 - 22:00, L.O. 20:30, 月, 定休日, Lunch 2024.9.17よりメニューリニューアル, Dinner 2024.9.20よりメニューリニューアル, 平日ランチタイム予約不可。, 状況に応じて対応させていただきます。, 水曜夜は、『TONKATSU  NIGHT』開催中！！, 「とんかつ御膳」はランチのみ提供しておりますが、夜も「とんかつ御膳を食べたい！！というお声を多くいただき、毎週水曜夜は特別に！「ランチ価格」でとんかつ御膳をお楽しみいただける「TONKATSU  NIGHT」を開催しております！, ・とんかつ御膳がランチ価格で！, ・大人気ドライリブが必ず１本ついてくる！, ※毎週水曜日ディナータイムはアラカルト、コースはおやすみさせていただきます。","月・水・木, 12:00 - 16:00, L.O. 料理15:30 ドリンク15:45, 17:30 - 23:00, L.O. 料理22:30 ドリンク22:50, 金, 12:00 - 16:00, L.O. 料理15:30 ドリンク15:45, 17:30 - 00:00, L.O. 料理23:30 ドリンク23:50, 土, 12:00 - 00:00, L.O. 料理23:30 ドリンク23:50, 日, 12:00 - 21:00, L.O. 20:30, 火, 定休日, 月一連休あり","月・火・水・木・金・土・日, 11:30 - 23:00, 祝日・祝前日・祝後日, 11:00 - 23:00, ■ 営業時間, アイドルタイムなしでの営業のため遅めのランチ、早めのディナーなど様々な用途でご利用いただけます。, 時間外でも定休日でのパーティー団体のご予約がある場合はお気軽にご相談ください。, ■ 定休日, なし(臨時休業などの場合はSNS等にてお知らせいたします)","11:00 - 16:00, L.O. 15:30, ■ 営業時間, ※現在は新型コロナウイルスの影響により、社員食堂(社員限定）として営業しております。安心安全な通常営業に戻れるように、準備しておりますので、しばしお待ちください。, ランチタイム 11:00～14:00, ※日替りランチ（数量限定）はなくなり次第終了ですm(__)m, ■ 定休日, 夏期、年末年始、 全社休業日 他, ※詳しくは店舗へお問い合わせください (^^)","月・水・木・金, 11:30 - 15:00, L.O. 料理14:00 ドリンク14:30, 17:30 - 22:00, L.O. 料理21:00 ドリンク21:30, 土・祝日, 11:30 - 15:00, L.O. 料理14:00 ドリンク14:30, 17:00 - 22:00, L.O. 料理21:00 ドリンク21:30, 日, 11:30 - 15:00, L.O. 料理14:00 ドリンク14:30, 17:00 - 21:00, L.O. 料理20:00 ドリンク20:30, 火, 定休日, ■火曜日が主祝日の場合は営業, ■年末年始は営業時間が変更となります。","月・火・水・木, 11:30 - 14:00, L.O. 13:30, 17:00 - 05:00, L.O. 04:00, 金・祝前日, 17:00 - 05:00, L.O. 04:00, 土・祝日, 11:30 - 15:00, L.O. 14:30, 15:00 - 05:00, L.O. 04:00, 日, 11:30 - 15:00, L.O. 14:30, 15:00 - 21:00, L.O. 20:00, 祝後日, 17:00 - 05:00, L.O. 料理04:00 ドリンク04:30, 最終入店時間は26時となりますのでご了承ください。, 食材の仕入れの状況により、閉店時間を早める場合があります。","火・水・木, 10:00 - 15:00, L.O. 14:30, 17:00 - 22:00, L.O. 料理21:00 ドリンク21:30, 金, 10:00 - 15:00, L.O. 14:30, 17:00 - 23:00, L.O. 料理22:00 ドリンク22:30, 土・祝前日, 08:30 - 15:00, L.O. 14:30, 17:00 - 23:00, L.O. 料理22:00 ドリンク22:30, 日・祝日, 08:30 - 15:00, L.O. 14:30, 17:00 - 22:00, L.O. 料理21:00 ドリンク21:30, 月, 定休日, ■ 定休日　月曜日, ＊月曜日が祝日の場合は営業になり、火曜日が定休日になります。"

import datetime
import json
from datetime import time

import requests

def gen_prompt(opening_hours_desc: str) -> str:

    nouns_map = {
        "L.O.": "餐厅在关门前的最后点餐时间。(实现函数以营业时间为准, 不考虑此时间)。",
        "祝日": "节假日, 对应函数get_opening_hours入参holidays数组, 营业时间包含节假日时节假日营业, 否则默认休息。",
        "年末年始": "年末年初，12月31日-1月3日。",
        "祝前日": "节假日的前一天",
        "祝後日": "节假日的后一天"
    }

    nouns = []
    idx = 1
    for noun, noun_desc in nouns_map.items():
        if noun in opening_hours_desc:
            nouns.append(f"  {idx}. \"{noun}\": {noun_desc}\n")
            idx = idx + 1
    nouns_str = "概念解释:\n" + "".join(nouns) if len(nouns) > 0 else ""

    s = """
根据日语营业时间: '*desc*';
实现计算目标日期(target_date) 的餐厅营业时间段的python函数: 'def get_opening_hours(target_date: datetime.date, holidays: list[datetime.date]) -> list[tuple[time, time]]:' ;

不要使用datetime以外的任何库!

*nouns*

返回json格式:{"func": "实现的python函数"}
                    """.replace("*desc*", opening_hours_desc).replace("*nouns*", nouns_str)

    print(s)
    return s


def gen_func(opening_hours_desc: str) -> str:

    # 构造 GPT-4o 消息
    messages = [{
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": gen_prompt(opening_hours_desc)
            }
        ]
    }]

    # 构造请求头
    headers = {
        "Content-Type": "application/json",
        "api-key": "********************************"
    }

    # 构造请求体
    payload = {
        "messages": messages,
        "temperature": 1,
        "max_tokens": 1000,
        "top_p": 0.95,
        "response_format": {"type": "json_object"}
    }

    # 发送请求

    response = requests.post(
        "https://133cn-westus3-new.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview",
        headers=headers,
        json=payload,
        timeout=30
    )
    response.raise_for_status()

    result_text = response.json()['choices'][0]['message']['content']
    return result_text

def run_str_func(str_func: str, target_date: datetime.date, holidays: list[datetime.date]):
    # 创建一个字典用于 exec 作用域，并包含 `datetime`
    exec_globals = {"datetime": datetime, "time": time}
    exec(str_func, exec_globals)  # 在 exec 作用域中运行代码

    # 获取 `get_opening_hours` 方法
    get_opening_hours = exec_globals["get_opening_hours"]

    # 调用 `get_opening_hours`
    return get_opening_hours(target_date, holidays)
if __name__ == '__main__':
    l = ["火・水・木・金, 17:00 - 23:00, 土・日・祝日, 16:00 - 23:00, 月, 定休日, 営業時間, 8/19(月).26(月)はお休みさせていただきます。, (平日) 17:00～23:00《Lo 22:00》, (土日祝) 16:00～23:00《Lo 22:00》, 閉店のお知らせ, 平素よりCARLOTTA をご利用いただきありがとうございます。, 誠に勝手ながら、当店は8月29日を持ちまして、閉店することとなりました。, 開店以来のご愛顧、誠にありがとうございました。, 店主　長崎, 営業時間・定休日は変更となる場合がございますので、ご来店前に店舗にご確認ください。","■ 営業時間, 月-木 MON-FRI, 11:30-15:30(15LO), 11:30-21:30(21LO), 土日祝 SAT SUN&Holiday, 11-21:30(21LO), 土日祝のランチタイムは混み合うことが多いため、お待たせしてしまうことがございます。お時間を少しずらして頂けるとスムーズなご案内ができるかと思います。ご了承ください。, ■ 定休日, 大晦日・元日（その他臨時休業の場合は公式HP、FB、インスタでお知らせいたします。ご確認ください。）"]
    for desc in l:
        #描述转函数
        func_str = gen_func(desc)
        print(func_str)
        #运行函数
        res = run_str_func(json.loads(func_str).get("func"), datetime.date(2025, 3, 18),holidays=[datetime.date(2025, 3, 28)])
        print(res)