import json
import pandas as pd

# JSON data
json_data = [
    {
        "createTime": "2025-01-24T14:44:22",
        "id": 4356,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19535789212"
    },
    {
        "createTime": "2025-01-24T14:40:47",
        "id": 4355,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13910166354"
    },
    {
        "createTime": "2025-01-24T14:39:21",
        "id": 4354,
        "receiverAddr": "贵州省黔西南州兴义市民航大道星汇峰镜2期",
        "receiverMobile": "18685989631",
        "receiverName": "刘灿",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18685989631"
    },
    {
        "createTime": "2025-01-24T14:32:54",
        "id": 4353,
        "receiverAddr": "贵州省黔西南州兴义市民航大道星汇峰镜2期",
        "receiverMobile": "18685989631",
        "receiverName": "刘灿",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18685989631"
    },
    {
        "createTime": "2025-01-24T14:30:12",
        "id": 4352,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17889458190"
    },
    {
        "createTime": "2025-01-24T14:21:44",
        "id": 4351,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13508716782"
    },
    {
        "createTime": "2025-01-24T14:12:46",
        "id": 4350,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13999119660"
    },
    {
        "createTime": "2025-01-24T13:51:17",
        "id": 4349,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18893495316"
    },
    {
        "createTime": "2025-01-24T13:47:45",
        "id": 4348,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13501125194"
    },
    {
        "createTime": "2025-01-24T13:38:40",
        "id": 4347,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13811902946"
    },
    {
        "createTime": "2025-01-24T13:28:58",
        "id": 4346,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19535789212"
    },
    {
        "createTime": "2025-01-24T13:09:35",
        "id": 4345,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13186535809"
    },
    {
        "createTime": "2025-01-24T13:07:44",
        "id": 4344,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18083285388"
    },
    {
        "createTime": "2025-01-24T12:58:17",
        "id": 4343,
        "receiverAddr": "黑龙江省齐齐哈尔市，龙沙区江安街道，广厦云起溪上 22 号楼 2 单元 1001",
        "receiverMobile": "18204666087",
        "receiverName": "高云朋",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18204666087"
    },
    {
        "createTime": "2025-01-24T12:38:28",
        "id": 4342,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13738183127"
    },
    {
        "createTime": "2025-01-24T12:36:39",
        "id": 4341,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17778014702"
    },
    {
        "createTime": "2025-01-24T12:35:45",
        "id": 4340,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13905430829"
    },
    {
        "createTime": "2025-01-24T12:23:34",
        "id": 4339,
        "receiverAddr": "北京市朝阳区沿海赛洛城402号楼",
        "receiverMobile": "18712985608",
        "receiverName": "王凡",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18712985608"
    },
    {
        "createTime": "2025-01-24T12:21:35",
        "id": 4338,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15116963931"
    },
    {
        "createTime": "2025-01-24T12:20:32",
        "id": 4337,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "16601240310"
    },
    {
        "createTime": "2025-01-24T12:12:00",
        "id": 4336,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13992700165"
    },
    {
        "createTime": "2025-01-24T11:42:14",
        "id": 4335,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13992700165"
    },
    {
        "createTime": "2025-01-24T11:39:25",
        "id": 4334,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13681391927"
    },
    {
        "createTime": "2025-01-24T11:35:43",
        "id": 4333,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15932607011"
    },
    {
        "createTime": "2025-01-24T11:33:35",
        "id": 4332,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13980048535"
    },
    {
        "createTime": "2025-01-24T11:25:07",
        "id": 4331,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院十三号楼二单元1002",
        "receiverMobile": "15303164190",
        "receiverName": "蔡宗伯",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15303164190"
    },
    {
        "createTime": "2025-01-24T11:07:36",
        "id": 4330,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18082733496"
    },
    {
        "createTime": "2025-01-24T11:02:48",
        "id": 4329,
        "receiverAddr": "北京市大兴区礼贤镇110地块礼贤家园13号楼二单元1602",
        "receiverMobile": "18631250076",
        "receiverName": "徐征宇",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18631250076"
    },
    {
        "createTime": "2025-01-24T11:00:42",
        "id": 4328,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13522126137"
    },
    {
        "createTime": "2025-01-24T10:53:25",
        "id": 4327,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15010450416"
    },
    {
        "createTime": "2025-01-24T10:48:17",
        "id": 4326,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13370146726"
    },
    {
        "createTime": "2025-01-24T10:43:36",
        "id": 4325,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13380061771"
    },
    {
        "createTime": "2025-01-24T10:23:26",
        "id": 4324,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-24T10:23:18",
        "id": 4323,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-24T09:55:35",
        "id": 4322,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13316099331"
    },
    {
        "createTime": "2025-01-24T09:54:33",
        "id": 4321,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18927708786"
    },
    {
        "createTime": "2025-01-24T09:38:34",
        "id": 4320,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13901033254"
    },
    {
        "createTime": "2025-01-24T09:08:23",
        "id": 4319,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13652464226"
    },
    {
        "createTime": "2025-01-24T08:47:45",
        "id": 4318,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13269633675"
    },
    {
        "createTime": "2025-01-24T08:47:38",
        "id": 4317,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13269633675"
    },
    {
        "createTime": "2025-01-24T08:47:31",
        "id": 4316,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13269633675"
    },
    {
        "createTime": "2025-01-24T08:17:29",
        "id": 4315,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院十三号楼二单元1002",
        "receiverMobile": "15303164190",
        "receiverName": "蔡宗伯",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15303164190"
    },
    {
        "createTime": "2025-01-24T08:17:19",
        "id": 4314,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院十三号楼二单元1002",
        "receiverMobile": "15303164190",
        "receiverName": "蔡宗伯",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15303164190"
    },
    {
        "createTime": "2025-01-24T05:44:57",
        "id": 4313,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19126982721"
    },
    {
        "createTime": "2025-01-24T02:46:37",
        "id": 4312,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13473130933"
    },
    {
        "createTime": "2025-01-23T21:41:18",
        "id": 4311,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520240034"
    },
    {
        "createTime": "2025-01-23T21:40:37",
        "id": 4310,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520240034"
    },
    {
        "createTime": "2025-01-23T20:55:16",
        "id": 4309,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18076944829"
    },
    {
        "createTime": "2025-01-23T20:18:05",
        "id": 4308,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13552335772"
    },
    {
        "createTime": "2025-01-23T20:16:11",
        "id": 4307,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17670612540"
    },
    {
        "createTime": "2025-01-23T20:15:55",
        "id": 4306,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17801181326"
    },
    {
        "createTime": "2025-01-23T20:00:46",
        "id": 4305,
        "receiverAddr": "陕西省榆林市榆阳区牛家梁镇",
        "receiverMobile": "13720686426",
        "receiverName": "张蓉",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13720686426"
    },
    {
        "createTime": "2025-01-23T19:50:30",
        "id": 4304,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18519192730"
    },
    {
        "createTime": "2025-01-23T19:45:05",
        "id": 4303,
        "receiverAddr": "北京市大兴区大兴国际机场航新路3号院5楼501卫卫卫卫",
        "receiverMobile": "13051532692",
        "receiverName": "薛琳",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13051532692"
    },
    {
        "createTime": "2025-01-23T19:29:23",
        "id": 4302,
        "receiverAddr": "上海市浦东新区惠南镇拱极路2626弄22号1002",
        "receiverMobile": "15801861915",
        "receiverName": "王凤晨芝",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15801861915"
    },
    {
        "createTime": "2025-01-23T19:28:53",
        "id": 4301,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15073893371"
    },
    {
        "createTime": "2025-01-23T19:28:32",
        "id": 4300,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13677335875"
    },
    {
        "createTime": "2025-01-23T19:28:31",
        "id": 4299,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13677335875"
    },
    {
        "createTime": "2025-01-23T19:25:53",
        "id": 4298,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13651223370"
    },
    {
        "createTime": "2025-01-23T19:16:11",
        "id": 4297,
        "receiverAddr": "陕西省榆林市榆阳区牛家梁镇",
        "receiverMobile": "13720686426",
        "receiverName": "张蓉",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13720686426"
    },
    {
        "createTime": "2025-01-23T19:14:33",
        "id": 4296,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13676896522"
    },
    {
        "createTime": "2025-01-23T19:08:35",
        "id": 4295,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18605029502"
    },
    {
        "createTime": "2025-01-23T19:05:24",
        "id": 4294,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18050106637"
    },
    {
        "createTime": "2025-01-23T19:02:07",
        "id": 4293,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18800467555"
    },
    {
        "createTime": "2025-01-23T18:41:29",
        "id": 4292,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18742038607"
    },
    {
        "createTime": "2025-01-23T18:39:59",
        "id": 4291,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13143742455"
    },
    {
        "createTime": "2025-01-23T18:03:24",
        "id": 4290,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18543108396"
    },
    {
        "createTime": "2025-01-23T17:53:32",
        "id": 4289,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18839875868"
    },
    {
        "createTime": "2025-01-23T17:18:55",
        "id": 4288,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17725506689"
    },
    {
        "createTime": "2025-01-23T17:18:21",
        "id": 4287,
        "receiverAddr": "北京市大兴区礼贤镇礼贤家园110地块13号楼二单元",
        "receiverMobile": "15010366291",
        "receiverName": "信源利",
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "16619986080"
    },
    {
        "createTime": "2025-01-23T17:14:00",
        "id": 4286,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15399283624"
    },
    {
        "createTime": "2025-01-23T17:06:24",
        "id": 4285,
        "receiverAddr": "福建省福州市台江区中平路132号海月江潮同安坊5～501",
        "receiverMobile": "18650090602",
        "receiverName": "林代代",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18650090602"
    },
    {
        "createTime": "2025-01-23T17:05:28",
        "id": 4284,
        "receiverAddr": "福建省福州市台江区中平路132号海月江潮同安坊5～501",
        "receiverMobile": "18650090602",
        "receiverName": "林代代",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18650090602"
    },
    {
        "createTime": "2025-01-23T16:53:53",
        "id": 4283,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520240034"
    },
    {
        "createTime": "2025-01-23T16:44:37",
        "id": 4282,
        "receiverAddr": "北京市大兴区礼贤镇礼贤家园110地块13号楼二单元",
        "receiverMobile": "15010366291",
        "receiverName": "信源利",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "16619986080"
    },
    {
        "createTime": "2025-01-23T16:20:22",
        "id": 4281,
        "receiverAddr": "北京市大兴区礼贤镇礼贤家园110地块13号楼二单元",
        "receiverMobile": "15010366291",
        "receiverName": "信源利",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "16619986080"
    },
    {
        "createTime": "2025-01-23T16:10:34",
        "id": 4280,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18602668156"
    },
    {
        "createTime": "2025-01-23T15:55:14",
        "id": 4279,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13538748220"
    },
    {
        "createTime": "2025-01-23T15:37:13",
        "id": 4278,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19989618956"
    },
    {
        "createTime": "2025-01-23T15:36:58",
        "id": 4277,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17372085606"
    },
    {
        "createTime": "2025-01-23T15:36:34",
        "id": 4276,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19989618956"
    },
    {
        "createTime": "2025-01-23T15:26:57",
        "id": 4275,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18511151955"
    },
    {
        "createTime": "2025-01-23T15:13:42",
        "id": 4274,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18616103697"
    },
    {
        "createTime": "2025-01-23T15:11:04",
        "id": 4273,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15548938383"
    },
    {
        "createTime": "2025-01-23T15:09:07",
        "id": 4272,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19535789212"
    },
    {
        "createTime": "2025-01-23T15:08:56",
        "id": 4271,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19535789212"
    },
    {
        "createTime": "2025-01-23T15:04:44",
        "id": 4270,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15811408199"
    },
    {
        "createTime": "2025-01-23T14:56:02",
        "id": 4269,
        "receiverAddr": "海南省三亚市吉阳区凤凰路79号凤岭悦澜",
        "receiverMobile": "15798967266",
        "receiverName": "罗嘉慧",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15798967266"
    },
    {
        "createTime": "2025-01-23T14:55:39",
        "id": 4268,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13611363412"
    },
    {
        "createTime": "2025-01-23T14:15:14",
        "id": 4267,
        "receiverAddr": "北京市大兴区礼贤镇礼贤家园110地块13号楼二单元",
        "receiverMobile": "15010366291",
        "receiverName": "信源利",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "16619986080"
    },
    {
        "createTime": "2025-01-23T14:13:49",
        "id": 4266,
        "receiverAddr": "北京市大兴区礼贤镇礼贤家园110地块13号楼二单元",
        "receiverMobile": "15010366291",
        "receiverName": "信源利",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "16619986080"
    },
    {
        "createTime": "2025-01-23T14:00:58",
        "id": 4265,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15010862192"
    },
    {
        "createTime": "2025-01-23T13:46:04",
        "id": 4264,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13581677470"
    },
    {
        "createTime": "2025-01-23T13:31:47",
        "id": 4263,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18968172359"
    },
    {
        "createTime": "2025-01-23T12:58:05",
        "id": 4262,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13995529461"
    },
    {
        "createTime": "2025-01-23T12:43:18",
        "id": 4261,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13596168096"
    },
    {
        "createTime": "2025-01-23T12:42:27",
        "id": 4260,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13596168096"
    },
    {
        "createTime": "2025-01-23T12:25:36",
        "id": 4259,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18611853414"
    },
    {
        "createTime": "2025-01-23T12:25:36",
        "id": 4258,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13824828499"
    },
    {
        "createTime": "2025-01-23T12:19:14",
        "id": 4257,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15319439652"
    },
    {
        "createTime": "2025-01-23T12:14:55",
        "id": 4256,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18604737997"
    },
    {
        "createTime": "2025-01-23T12:11:58",
        "id": 4255,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13765466541"
    },
    {
        "createTime": "2025-01-23T11:33:53",
        "id": 4254,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13560890171"
    },
    {
        "createTime": "2025-01-23T11:32:25",
        "id": 4253,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13560890171"
    },
    {
        "createTime": "2025-01-23T11:27:16",
        "id": 4252,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18993339304"
    },
    {
        "createTime": "2025-01-23T11:19:26",
        "id": 4251,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院十三号楼二单元1002",
        "receiverMobile": "15303164190",
        "receiverName": "蔡宗伯",
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15303164190"
    },
    {
        "createTime": "2025-01-23T11:04:41",
        "id": 4250,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15210693835"
    },
    {
        "createTime": "2025-01-23T11:04:21",
        "id": 4249,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-23T11:04:12",
        "id": 4248,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-23T11:04:06",
        "id": 4247,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-23T11:03:58",
        "id": 4246,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-23T11:03:51",
        "id": 4245,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-23T10:41:44",
        "id": 4244,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15010919351"
    },
    {
        "createTime": "2025-01-23T10:11:32",
        "id": 4243,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13649775727"
    },
    {
        "createTime": "2025-01-23T10:03:59",
        "id": 4242,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13570878541"
    },
    {
        "createTime": "2025-01-23T09:54:57",
        "id": 4241,
        "receiverAddr": "朝阳区广渠路百环家园西区7号楼509",
        "receiverMobile": "18627745135",
        "receiverName": "唐荣",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18627745135"
    },
    {
        "createTime": "2025-01-23T09:54:46",
        "id": 4240,
        "receiverAddr": "朝阳区广渠路百环家园西区7号楼509",
        "receiverMobile": "18627745135",
        "receiverName": "唐荣",
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18627745135"
    },
    {
        "createTime": "2025-01-23T09:54:38",
        "id": 4239,
        "receiverAddr": "朝阳区广渠路百环家园西区7号楼509",
        "receiverMobile": "18627745135",
        "receiverName": "唐荣",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18627745135"
    },
    {
        "createTime": "2025-01-23T09:51:35",
        "id": 4238,
        "receiverAddr": "朝阳区广渠路百环家园西区7号楼509",
        "receiverMobile": "18627745135",
        "receiverName": "唐荣",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18627745135"
    },
    {
        "createTime": "2025-01-23T09:51:25",
        "id": 4237,
        "receiverAddr": "朝阳区广渠路百环家园西区7号楼509",
        "receiverMobile": "18627745135",
        "receiverName": "唐荣",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18627745135"
    },
    {
        "createTime": "2025-01-23T09:51:17",
        "id": 4236,
        "receiverAddr": "朝阳区广渠路百环家园西区7号楼509",
        "receiverMobile": "18627745135",
        "receiverName": "唐荣",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18627745135"
    },
    {
        "createTime": "2025-01-23T09:45:53",
        "id": 4235,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15801210238"
    },
    {
        "createTime": "2025-01-23T08:56:57",
        "id": 4234,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15370651529"
    },
    {
        "createTime": "2025-01-23T08:51:10",
        "id": 4233,
        "receiverAddr": "内蒙古锡林浩特市锡湖世家百米驿站",
        "receiverMobile": "13654896638",
        "receiverName": "司南",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13654896638"
    },
    {
        "createTime": "2025-01-23T08:47:21",
        "id": 4232,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15370651529"
    },
    {
        "createTime": "2025-01-23T07:55:55",
        "id": 4231,
        "receiverAddr": "北京市朝阳区黄木厂路1号院1号楼3单元1502",
        "receiverMobile": "13552988043",
        "receiverName": "高婷婷",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13552988043"
    },
    {
        "createTime": "2025-01-23T02:04:30",
        "id": 4230,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15387454207"
    },
    {
        "createTime": "2025-01-22T21:00:16",
        "id": 4229,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13810271267"
    },
    {
        "createTime": "2025-01-22T20:35:52",
        "id": 4228,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18015734001"
    },
    {
        "createTime": "2025-01-22T20:34:16",
        "id": 4227,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15330289762"
    },
    {
        "createTime": "2025-01-22T20:19:17",
        "id": 4226,
        "receiverAddr": "广东省惠州市惠城区桥东街道东湖九区北门",
        "receiverMobile": "13927301683",
        "receiverName": "吴海康",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13927301683"
    },
    {
        "createTime": "2025-01-22T20:18:12",
        "id": 4225,
        "receiverAddr": "广东省惠州市惠城区桥东街道东湖九区北门",
        "receiverMobile": "13927301683",
        "receiverName": "吴海康",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13927301683"
    },
    {
        "createTime": "2025-01-22T19:58:02",
        "id": 4224,
        "receiverAddr": "北京市大兴区大兴国际机场航新路3号院5楼501卫卫卫卫",
        "receiverMobile": "13051532692",
        "receiverName": "薛琳",
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13051532692"
    },
    {
        "createTime": "2025-01-22T19:53:38",
        "id": 4223,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13450255228"
    },
    {
        "createTime": "2025-01-22T19:44:47",
        "id": 4222,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13633419111"
    },
    {
        "createTime": "2025-01-22T19:30:06",
        "id": 4221,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15970667729"
    },
    {
        "createTime": "2025-01-22T19:22:12",
        "id": 4220,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18611018030"
    },
    {
        "createTime": "2025-01-22T18:59:47",
        "id": 4219,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13488957735"
    },
    {
        "createTime": "2025-01-22T18:30:43",
        "id": 4218,
        "receiverAddr": "北京市大兴区礼贤镇礼贤家园110地块13号楼二单元",
        "receiverMobile": "15010366291",
        "receiverName": "信源利",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "16619986080"
    },
    {
        "createTime": "2025-01-22T18:27:30",
        "id": 4217,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13380227396"
    },
    {
        "createTime": "2025-01-22T18:18:30",
        "id": 4216,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18310921103"
    },
    {
        "createTime": "2025-01-22T17:44:21",
        "id": 4215,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13902256108"
    },
    {
        "createTime": "2025-01-22T17:38:26",
        "id": 4214,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13761745387"
    },
    {
        "createTime": "2025-01-22T17:33:32",
        "id": 4213,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15859249104"
    },
    {
        "createTime": "2025-01-22T17:18:06",
        "id": 4212,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18838220402"
    },
    {
        "createTime": "2025-01-22T16:59:21",
        "id": 4211,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18513030372"
    },
    {
        "createTime": "2025-01-22T16:57:33",
        "id": 4210,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17720759081"
    },
    {
        "createTime": "2025-01-22T16:41:46",
        "id": 4209,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13788026098"
    },
    {
        "createTime": "2025-01-22T16:36:12",
        "id": 4208,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13936788458"
    },
    {
        "createTime": "2025-01-22T15:53:31",
        "id": 4207,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15810434527"
    },
    {
        "createTime": "2025-01-22T15:44:28",
        "id": 4206,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15302197381"
    },
    {
        "createTime": "2025-01-22T15:06:23",
        "id": 4205,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13656583920"
    },
    {
        "createTime": "2025-01-22T14:36:46",
        "id": 4204,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17692410632"
    },
    {
        "createTime": "2025-01-22T14:36:31",
        "id": 4203,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17692410632"
    },
    {
        "createTime": "2025-01-22T14:35:22",
        "id": 4202,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13450396029"
    },
    {
        "createTime": "2025-01-22T14:17:18",
        "id": 4201,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19987433579"
    },
    {
        "createTime": "2025-01-22T14:07:48",
        "id": 4200,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15776583333"
    },
    {
        "createTime": "2025-01-22T14:07:24",
        "id": 4199,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18210250678"
    },
    {
        "createTime": "2025-01-22T13:58:58",
        "id": 4198,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13945365956"
    },
    {
        "createTime": "2025-01-22T13:58:13",
        "id": 4197,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18612273990"
    },
    {
        "createTime": "2025-01-22T13:51:01",
        "id": 4196,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13488682719"
    },
    {
        "createTime": "2025-01-22T13:50:50",
        "id": 4195,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13488682719"
    },
    {
        "createTime": "2025-01-22T13:37:12",
        "id": 4194,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13522530530"
    },
    {
        "createTime": "2025-01-22T13:27:01",
        "id": 4193,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15776583333"
    },
    {
        "createTime": "2025-01-22T13:03:36",
        "id": 4192,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13581872338"
    },
    {
        "createTime": "2025-01-22T12:56:52",
        "id": 4191,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15990608009"
    },
    {
        "createTime": "2025-01-22T12:44:26",
        "id": 4190,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13521210922"
    },
    {
        "createTime": "2025-01-22T12:36:01",
        "id": 4189,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13664830551"
    },
    {
        "createTime": "2025-01-22T12:21:41",
        "id": 4188,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13653029080"
    },
    {
        "createTime": "2025-01-22T12:11:22",
        "id": 4187,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13890112688"
    },
    {
        "createTime": "2025-01-22T12:11:15",
        "id": 4186,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13890112688"
    },
    {
        "createTime": "2025-01-22T11:58:43",
        "id": 4185,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13351117899"
    },
    {
        "createTime": "2025-01-22T11:50:08",
        "id": 4184,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15811377724"
    },
    {
        "createTime": "2025-01-22T11:50:04",
        "id": 4183,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18909896928"
    },
    {
        "createTime": "2025-01-22T11:45:35",
        "id": 4182,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13756101631"
    },
    {
        "createTime": "2025-01-22T11:43:06",
        "id": 4181,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15776583333"
    },
    {
        "createTime": "2025-01-22T11:37:33",
        "id": 4180,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13998821609"
    },
    {
        "createTime": "2025-01-22T11:35:22",
        "id": 4179,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15602966771"
    },
    {
        "createTime": "2025-01-22T11:31:19",
        "id": 4178,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19312591592"
    },
    {
        "createTime": "2025-01-22T11:25:58",
        "id": 4177,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13601121691"
    },
    {
        "createTime": "2025-01-22T11:23:38",
        "id": 4176,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13834415610"
    },
    {
        "createTime": "2025-01-22T11:17:56",
        "id": 4175,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13314317375"
    },
    {
        "createTime": "2025-01-22T11:17:54",
        "id": 4174,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15811584418"
    },
    {
        "createTime": "2025-01-22T11:17:45",
        "id": 4173,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13314317375"
    },
    {
        "createTime": "2025-01-22T11:09:57",
        "id": 4172,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13621195393"
    },
    {
        "createTime": "2025-01-22T10:53:59",
        "id": 4171,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15942295668"
    },
    {
        "createTime": "2025-01-22T10:15:55",
        "id": 4170,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13693687553"
    },
    {
        "createTime": "2025-01-22T10:02:55",
        "id": 4169,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15814963157"
    },
    {
        "createTime": "2025-01-22T09:02:56",
        "id": 4168,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18611018030"
    },
    {
        "createTime": "2025-01-22T08:56:23",
        "id": 4167,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17709478567"
    },
    {
        "createTime": "2025-01-22T08:43:50",
        "id": 4166,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15811186703"
    },
    {
        "createTime": "2025-01-22T08:34:31",
        "id": 4165,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15831683349"
    },
    {
        "createTime": "2025-01-22T08:33:45",
        "id": 4164,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-22T08:33:37",
        "id": 4163,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-22T08:33:28",
        "id": 4162,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-22T08:33:21",
        "id": 4161,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-22T08:15:39",
        "id": 4160,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18395921135"
    },
    {
        "createTime": "2025-01-22T08:02:50",
        "id": 4159,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610290623"
    },
    {
        "createTime": "2025-01-22T08:02:38",
        "id": 4158,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610290623"
    },
    {
        "createTime": "2025-01-22T08:02:27",
        "id": 4157,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610290623"
    },
    {
        "createTime": "2025-01-22T06:32:52",
        "id": 4156,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18677964302"
    },
    {
        "createTime": "2025-01-22T04:32:49",
        "id": 4155,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13329478338"
    },
    {
        "createTime": "2025-01-22T01:12:41",
        "id": 4154,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13286483805"
    },
    {
        "createTime": "2025-01-22T01:06:31",
        "id": 4153,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15918780480"
    },
    {
        "createTime": "2025-01-21T20:46:32",
        "id": 4152,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18809990318"
    },
    {
        "createTime": "2025-01-21T20:13:25",
        "id": 4151,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13942836781"
    },
    {
        "createTime": "2025-01-21T20:11:04",
        "id": 4150,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13760373186"
    },
    {
        "createTime": "2025-01-21T19:48:11",
        "id": 4149,
        "receiverAddr": "朝阳区广渠路百环家园西区7号楼509",
        "receiverMobile": "18627745135",
        "receiverName": "唐荣",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18627745135"
    },
    {
        "createTime": "2025-01-21T19:38:03",
        "id": 4148,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13638796638"
    },
    {
        "createTime": "2025-01-21T19:16:40",
        "id": 4147,
        "receiverAddr": "朝阳区广渠路百环家园西区7号楼509",
        "receiverMobile": "18627745135",
        "receiverName": "唐荣",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_WAIT",
        "statusVal": 1,
        "userMobile": "18627745135"
    },
    {
        "createTime": "2025-01-21T19:15:58",
        "id": 4146,
        "receiverAddr": "朝阳区广渠路百环家园西区7号楼509",
        "receiverMobile": "18627745135",
        "receiverName": "唐荣",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18627745135"
    },
    {
        "createTime": "2025-01-21T19:15:47",
        "id": 4145,
        "receiverAddr": "朝阳区广渠路百环家园西区7号楼509",
        "receiverMobile": "18627745135",
        "receiverName": "唐荣",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18627745135"
    },
    {
        "createTime": "2025-01-21T19:15:32",
        "id": 4144,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18611018030"
    },
    {
        "createTime": "2025-01-21T19:13:57",
        "id": 4143,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18611018030"
    },
    {
        "createTime": "2025-01-21T19:13:41",
        "id": 4142,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18302983288"
    },
    {
        "createTime": "2025-01-21T19:13:30",
        "id": 4141,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15961748280"
    },
    {
        "createTime": "2025-01-21T19:12:42",
        "id": 4140,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13581821636"
    },
    {
        "createTime": "2025-01-21T18:56:47",
        "id": 4139,
        "receiverAddr": "四川省, 成都市 武侯区, 绿地gic一期菜鸟驿站",
        "receiverMobile": "18657138999",
        "receiverName": "黄装修",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18657138099"
    },
    {
        "createTime": "2025-01-21T18:34:01",
        "id": 4138,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18750550897"
    },
    {
        "createTime": "2025-01-21T17:54:26",
        "id": 4137,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13074880580"
    },
    {
        "createTime": "2025-01-21T17:43:25",
        "id": 4136,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18824723531"
    },
    {
        "createTime": "2025-01-21T17:24:43",
        "id": 4135,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "98383246"
    },
    {
        "createTime": "2025-01-21T17:02:22",
        "id": 4134,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19846338189"
    },
    {
        "createTime": "2025-01-21T16:52:23",
        "id": 4133,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13829958666"
    },
    {
        "createTime": "2025-01-21T16:47:04",
        "id": 4132,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18742085297"
    },
    {
        "createTime": "2025-01-21T16:46:57",
        "id": 4131,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18742085297"
    },
    {
        "createTime": "2025-01-21T16:46:29",
        "id": 4130,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18742085297"
    },
    {
        "createTime": "2025-01-21T16:46:17",
        "id": 4129,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18742085297"
    },
    {
        "createTime": "2025-01-21T16:19:50",
        "id": 4128,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_WAIT",
        "statusVal": 1,
        "userMobile": "19535789212"
    },
    {
        "createTime": "2025-01-21T16:16:32",
        "id": 4127,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15324114934"
    },
    {
        "createTime": "2025-01-21T16:11:41",
        "id": 4126,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13718937054"
    },
    {
        "createTime": "2025-01-21T15:47:58",
        "id": 4125,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "432210226"
    },
    {
        "createTime": "2025-01-21T15:44:57",
        "id": 4124,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15645326566"
    },
    {
        "createTime": "2025-01-21T15:42:37",
        "id": 4123,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18646632555"
    },
    {
        "createTime": "2025-01-21T15:39:41",
        "id": 4122,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18670032345"
    },
    {
        "createTime": "2025-01-21T15:29:55",
        "id": 4121,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13911863768"
    },
    {
        "createTime": "2025-01-21T15:10:04",
        "id": 4120,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13273609849"
    },
    {
        "createTime": "2025-01-21T15:08:26",
        "id": 4119,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13661007170"
    },
    {
        "createTime": "2025-01-21T14:57:32",
        "id": 4118,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13452365840"
    },
    {
        "createTime": "2025-01-21T14:45:28",
        "id": 4117,
        "receiverAddr": "北京市大兴区礼贤镇礼贤家园110地块13号楼二单元",
        "receiverMobile": "15010366291",
        "receiverName": "信源利",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "16619986080"
    },
    {
        "createTime": "2025-01-21T14:40:49",
        "id": 4116,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-21T14:40:12",
        "id": 4115,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18276404515"
    },
    {
        "createTime": "2025-01-21T14:30:22",
        "id": 4114,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15626988339"
    },
    {
        "createTime": "2025-01-21T14:29:26",
        "id": 4113,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15399055818"
    },
    {
        "createTime": "2025-01-21T14:18:45",
        "id": 4112,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-21T14:18:38",
        "id": 4111,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-21T14:06:46",
        "id": 4110,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13349596119"
    },
    {
        "createTime": "2025-01-21T13:54:31",
        "id": 4109,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18785111642"
    },
    {
        "createTime": "2025-01-21T13:51:33",
        "id": 4108,
        "receiverAddr": "北京市朝阳区百子湾家园301-1-703",
        "receiverMobile": "13718962418",
        "receiverName": "杨凤云",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610021314"
    },
    {
        "createTime": "2025-01-21T13:38:33",
        "id": 4107,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19130031126"
    },
    {
        "createTime": "2025-01-21T13:11:36",
        "id": 4106,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13811530357"
    },
    {
        "createTime": "2025-01-21T13:09:49",
        "id": 4105,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610029689"
    },
    {
        "createTime": "2025-01-21T13:05:46",
        "id": 4104,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13811530357"
    },
    {
        "createTime": "2025-01-21T12:58:00",
        "id": 4103,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13450106903"
    },
    {
        "createTime": "2025-01-21T12:18:56",
        "id": 4102,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15967951678"
    },
    {
        "createTime": "2025-01-21T12:18:32",
        "id": 4101,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13701250510"
    },
    {
        "createTime": "2025-01-21T11:43:27",
        "id": 4100,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13597996324"
    },
    {
        "createTime": "2025-01-21T11:24:53",
        "id": 4099,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18810029959"
    },
    {
        "createTime": "2025-01-21T11:22:08",
        "id": 4098,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13993771510"
    },
    {
        "createTime": "2025-01-21T11:19:13",
        "id": 4097,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15811186703"
    },
    {
        "createTime": "2025-01-21T11:16:25",
        "id": 4096,
        "receiverAddr": "福建省宁德市蕉城区华府豪庭13号楼1104室",
        "receiverMobile": "18650510900",
        "receiverName": "李小莉",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18650510900"
    },
    {
        "createTime": "2025-01-21T11:01:04",
        "id": 4095,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520332403"
    },
    {
        "createTime": "2025-01-21T11:00:37",
        "id": 4094,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520332403"
    },
    {
        "createTime": "2025-01-21T10:58:56",
        "id": 4093,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13716362580"
    },
    {
        "createTime": "2025-01-21T10:58:29",
        "id": 4092,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18232666422"
    },
    {
        "createTime": "2025-01-21T10:39:47",
        "id": 4091,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15100727529"
    },
    {
        "createTime": "2025-01-21T10:24:23",
        "id": 4090,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18502717524"
    },
    {
        "createTime": "2025-01-21T10:23:41",
        "id": 4089,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-21T10:21:43",
        "id": 4088,
        "receiverAddr": "河北省廊坊市永清镇北岔口",
        "receiverMobile": "18533604212",
        "receiverName": "李女士",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18533604212"
    },
    {
        "createTime": "2025-01-21T10:15:59",
        "id": 4087,
        "receiverAddr": "北京市大兴区礼贤家园110地块13号楼2单元1602",
        "receiverMobile": "19223015670",
        "receiverName": "张岩",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19223015670"
    },
    {
        "createTime": "2025-01-21T10:15:51",
        "id": 4086,
        "receiverAddr": "北京市大兴区礼贤家园110地块13号楼2单元1602",
        "receiverMobile": "19223015670",
        "receiverName": "张岩",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19223015670"
    },
    {
        "createTime": "2025-01-21T09:00:05",
        "id": 4085,
        "receiverAddr": "北京市大兴区礼贤镇110地块礼贤家园13号楼二单元1602",
        "receiverMobile": "18631250076",
        "receiverName": "徐征宇",
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18631250076"
    },
    {
        "createTime": "2025-01-21T08:29:56",
        "id": 4084,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18811664586"
    },
    {
        "createTime": "2025-01-21T08:22:51",
        "id": 4083,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18510001804"
    },
    {
        "createTime": "2025-01-21T08:10:33",
        "id": 4082,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19529742874"
    },
    {
        "createTime": "2025-01-21T08:05:17",
        "id": 4081,
        "receiverAddr": "现场拿",
        "receiverMobile": "18518375595",
        "receiverName": "李聪聪",
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18518375595"
    },
    {
        "createTime": "2025-01-21T08:00:35",
        "id": 4080,
        "receiverAddr": "北京市大兴区大兴国际机场东航倒班宿舍A1-619",
        "receiverMobile": "17301122829",
        "receiverName": "张成",
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17301122829"
    },
    {
        "createTime": "2025-01-21T05:20:14",
        "id": 4079,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18911157774"
    },
    {
        "createTime": "2025-01-21T00:43:15",
        "id": 4078,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13657448206"
    },
    {
        "createTime": "2025-01-20T20:47:42",
        "id": 4077,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17688922457"
    },
    {
        "createTime": "2025-01-20T20:40:26",
        "id": 4076,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18419904295"
    },
    {
        "createTime": "2025-01-20T20:40:19",
        "id": 4075,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18419904295"
    },
    {
        "createTime": "2025-01-20T19:56:17",
        "id": 4074,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15066645887"
    },
    {
        "createTime": "2025-01-20T19:51:57",
        "id": 4073,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19932169527"
    },
    {
        "createTime": "2025-01-20T19:48:05",
        "id": 4072,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15874095328"
    },
    {
        "createTime": "2025-01-20T19:47:40",
        "id": 4071,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15801434661"
    },
    {
        "createTime": "2025-01-20T19:39:26",
        "id": 4070,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13130340159"
    },
    {
        "createTime": "2025-01-20T19:20:10",
        "id": 4069,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_WAIT",
        "statusVal": 1,
        "userMobile": "18932721759"
    },
    {
        "createTime": "2025-01-20T19:15:59",
        "id": 4068,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13502855186"
    },
    {
        "createTime": "2025-01-20T19:12:01",
        "id": 4067,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13611748050"
    },
    {
        "createTime": "2025-01-20T19:06:01",
        "id": 4066,
        "receiverAddr": "现场拿",
        "receiverMobile": "18518375595",
        "receiverName": "李聪聪",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18518375595"
    },
    {
        "createTime": "2025-01-20T19:05:30",
        "id": 4065,
        "receiverAddr": "现场拿",
        "receiverMobile": "18518375595",
        "receiverName": "李聪聪",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18518375595"
    },
    {
        "createTime": "2025-01-20T19:05:20",
        "id": 4064,
        "receiverAddr": "现场拿",
        "receiverMobile": "18518375595",
        "receiverName": "李聪聪",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18518375595"
    },
    {
        "createTime": "2025-01-20T19:04:26",
        "id": 4063,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15923212269"
    },
    {
        "createTime": "2025-01-20T19:03:41",
        "id": 4062,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13241738562"
    },
    {
        "createTime": "2025-01-20T19:03:37",
        "id": 4061,
        "receiverAddr": "现场拿",
        "receiverMobile": "18518375595",
        "receiverName": "李聪聪",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18518375595"
    },
    {
        "createTime": "2025-01-20T19:02:33",
        "id": 4060,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13947062468"
    },
    {
        "createTime": "2025-01-20T19:01:03",
        "id": 4059,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T19:00:56",
        "id": 4058,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T19:00:38",
        "id": 4057,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T19:00:31",
        "id": 4056,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T19:00:16",
        "id": 4055,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T19:00:09",
        "id": 4054,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T18:59:59",
        "id": 4053,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T18:59:43",
        "id": 4052,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T18:59:36",
        "id": 4051,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T18:59:22",
        "id": 4050,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T18:59:16",
        "id": 4049,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T18:59:06",
        "id": 4048,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T18:58:58",
        "id": 4047,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T18:58:50",
        "id": 4046,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T18:58:34",
        "id": 4045,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-20T18:58:11",
        "id": 4044,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13911135612"
    },
    {
        "createTime": "2025-01-20T18:33:49",
        "id": 4043,
        "receiverAddr": "北京市大兴区礼贤镇礼贤家园110地块13号楼二单元",
        "receiverMobile": "15010366291",
        "receiverName": "信源利",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "16619986080"
    },
    {
        "createTime": "2025-01-20T18:32:54",
        "id": 4042,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13981735313"
    },
    {
        "createTime": "2025-01-20T18:31:56",
        "id": 4041,
        "receiverAddr": "海南省三亚市崖州区崖州湾壹号5号楼1510",
        "receiverMobile": "13401035993",
        "receiverName": "张若琳",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13401035993"
    },
    {
        "createTime": "2025-01-20T17:41:41",
        "id": 4040,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18757996906"
    },
    {
        "createTime": "2025-01-20T17:33:14",
        "id": 4039,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18991817663"
    },
    {
        "createTime": "2025-01-20T17:29:52",
        "id": 4038,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18757996906"
    },
    {
        "createTime": "2025-01-20T17:28:31",
        "id": 4037,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13514389702"
    },
    {
        "createTime": "2025-01-20T17:26:40",
        "id": 4036,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13600070700"
    },
    {
        "createTime": "2025-01-20T16:59:22",
        "id": 4035,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18311155832"
    },
    {
        "createTime": "2025-01-20T16:49:33",
        "id": 4034,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19177147201"
    },
    {
        "createTime": "2025-01-20T16:44:57",
        "id": 4033,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-20T16:44:50",
        "id": 4032,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-20T16:44:43",
        "id": 4031,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-20T16:44:35",
        "id": 4030,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-20T16:44:22",
        "id": 4029,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15960211087"
    },
    {
        "createTime": "2025-01-20T16:33:09",
        "id": 4028,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13999193529"
    },
    {
        "createTime": "2025-01-20T15:55:18",
        "id": 4027,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15375785303"
    },
    {
        "createTime": "2025-01-20T15:51:36",
        "id": 4026,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18810558199"
    },
    {
        "createTime": "2025-01-20T15:30:19",
        "id": 4025,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18030250956"
    },
    {
        "createTime": "2025-01-20T15:27:46",
        "id": 4024,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18611018030"
    },
    {
        "createTime": "2025-01-20T15:25:35",
        "id": 4023,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18511659830"
    },
    {
        "createTime": "2025-01-20T15:24:23",
        "id": 4022,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院十三号楼二单元1002",
        "receiverMobile": "15303164190",
        "receiverName": "蔡宗伯",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15303164190"
    },
    {
        "createTime": "2025-01-20T15:23:18",
        "id": 4021,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13850075390"
    },
    {
        "createTime": "2025-01-20T15:19:43",
        "id": 4020,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13958216776"
    },
    {
        "createTime": "2025-01-20T15:16:25",
        "id": 4019,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18903230261"
    },
    {
        "createTime": "2025-01-20T15:04:52",
        "id": 4018,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15960111199"
    },
    {
        "createTime": "2025-01-20T14:56:24",
        "id": 4017,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13770083939"
    },
    {
        "createTime": "2025-01-20T14:37:33",
        "id": 4016,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17720540652"
    },
    {
        "createTime": "2025-01-20T14:17:11",
        "id": 4015,
        "receiverAddr": "广东省东莞市南城区滨河路36号恒大御景苑12栋2单元904",
        "receiverMobile": "15014800307",
        "receiverName": "张小平",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15014800307"
    },
    {
        "createTime": "2025-01-20T14:03:02",
        "id": 4014,
        "receiverAddr": "北京市大兴区大兴国际机场东航倒班宿舍A1-619",
        "receiverMobile": "17301122829",
        "receiverName": "张成",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17301122829"
    },
    {
        "createTime": "2025-01-20T13:59:15",
        "id": 4013,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18232666422"
    },
    {
        "createTime": "2025-01-20T13:58:56",
        "id": 4012,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18232666422"
    },
    {
        "createTime": "2025-01-20T13:58:34",
        "id": 4011,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18232666422"
    },
    {
        "createTime": "2025-01-20T13:57:45",
        "id": 4010,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18232666422"
    },
    {
        "createTime": "2025-01-20T13:54:19",
        "id": 4009,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18964851430"
    },
    {
        "createTime": "2025-01-20T13:50:33",
        "id": 4008,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13127363252"
    },
    {
        "createTime": "2025-01-20T13:44:30",
        "id": 4007,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15051815623"
    },
    {
        "createTime": "2025-01-20T13:40:45",
        "id": 4006,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18982389498"
    },
    {
        "createTime": "2025-01-20T13:39:51",
        "id": 4005,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18982389498"
    },
    {
        "createTime": "2025-01-20T13:31:43",
        "id": 4004,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18612929001"
    },
    {
        "createTime": "2025-01-20T13:20:27",
        "id": 4003,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-20T13:19:46",
        "id": 4002,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-20T13:19:39",
        "id": 4001,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-20T13:19:31",
        "id": 4000,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-20T13:19:25",
        "id": 3999,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-20T13:08:50",
        "id": 3998,
        "receiverAddr": "北京市大兴区乐园路22号金地仰山东区11号楼二单元602",
        "receiverMobile": "18515001283",
        "receiverName": "吕睿",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18515001283"
    },
    {
        "createTime": "2025-01-20T13:04:03",
        "id": 3997,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13876677166"
    },
    {
        "createTime": "2025-01-20T12:33:35",
        "id": 3996,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13811578716"
    },
    {
        "createTime": "2025-01-20T12:17:10",
        "id": 3995,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18233822691"
    },
    {
        "createTime": "2025-01-20T12:10:13",
        "id": 3994,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13999416892"
    },
    {
        "createTime": "2025-01-20T11:14:16",
        "id": 3993,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18818238957"
    },
    {
        "createTime": "2025-01-20T11:03:44",
        "id": 3992,
        "receiverAddr": "北京市大兴区大兴国际机场东航倒班宿舍A1-619",
        "receiverMobile": "17301122829",
        "receiverName": "张成",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17301122829"
    },
    {
        "createTime": "2025-01-20T10:46:09",
        "id": 3991,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18368230112"
    },
    {
        "createTime": "2025-01-20T09:36:45",
        "id": 3990,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-20T08:23:44",
        "id": 3989,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13521976919"
    },
    {
        "createTime": "2025-01-20T08:13:54",
        "id": 3988,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17817212313"
    },
    {
        "createTime": "2025-01-20T08:08:25",
        "id": 3987,
        "receiverAddr": "北京市大兴区西红门理想家园19-1111",
        "receiverMobile": "15727380628",
        "receiverName": "李元",
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15727380628"
    },
    {
        "createTime": "2025-01-20T08:03:03",
        "id": 3986,
        "receiverAddr": "北京市大兴区西红门理想家园19-1111",
        "receiverMobile": "15727380628",
        "receiverName": "李元",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15727380628"
    },
    {
        "createTime": "2025-01-20T08:02:55",
        "id": 3985,
        "receiverAddr": "北京市大兴区西红门理想家园19-1111",
        "receiverMobile": "15727380628",
        "receiverName": "李元",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15727380628"
    },
    {
        "createTime": "2025-01-20T06:50:09",
        "id": 3984,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13231899696"
    },
    {
        "createTime": "2025-01-20T01:25:30",
        "id": 3983,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17614623238"
    },
    {
        "createTime": "2025-01-19T19:45:37",
        "id": 3982,
        "receiverAddr": "北京市石景山区中海天玺5-2-1002",
        "receiverMobile": "18600259731",
        "receiverName": "刘梓晨",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18600259731"
    },
    {
        "createTime": "2025-01-19T19:40:08",
        "id": 3981,
        "receiverAddr": "北京市大兴区大兴国际机场航新路3号院5楼501卫卫卫卫",
        "receiverMobile": "13051532692",
        "receiverName": "薛琳",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13051532692"
    },
    {
        "createTime": "2025-01-19T19:13:53",
        "id": 3980,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13631160975"
    },
    {
        "createTime": "2025-01-19T19:13:34",
        "id": 3979,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13802678062"
    },
    {
        "createTime": "2025-01-19T18:45:16",
        "id": 3978,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17858488417"
    },
    {
        "createTime": "2025-01-19T18:43:57",
        "id": 3977,
        "receiverAddr": "收件人: 邹枥剑 手机号码: 17551802321 所在地区: 四川省成都市新津区普兴街道 详细地址: 恒大天府城邦5期3栋2单元1302",
        "receiverMobile": "17551802321",
        "receiverName": "邹枥剑",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17551802321"
    },
    {
        "createTime": "2025-01-19T18:37:11",
        "id": 3976,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15859638993"
    },
    {
        "createTime": "2025-01-19T18:28:20",
        "id": 3975,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18911051299"
    },
    {
        "createTime": "2025-01-19T18:28:08",
        "id": 3974,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18911051299"
    },
    {
        "createTime": "2025-01-19T17:44:47",
        "id": 3973,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13011159932"
    },
    {
        "createTime": "2025-01-19T17:31:39",
        "id": 3972,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17332154657"
    },
    {
        "createTime": "2025-01-19T17:19:15",
        "id": 3971,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15988325457"
    },
    {
        "createTime": "2025-01-19T16:48:41",
        "id": 3970,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15811395966"
    },
    {
        "createTime": "2025-01-19T16:46:06",
        "id": 3969,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18599264212"
    },
    {
        "createTime": "2025-01-19T16:40:14",
        "id": 3968,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13823166401"
    },
    {
        "createTime": "2025-01-19T16:40:03",
        "id": 3967,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13823166401"
    },
    {
        "createTime": "2025-01-19T16:26:27",
        "id": 3966,
        "receiverAddr": "重庆市渝北区万科城哲园",
        "receiverMobile": "17723165167",
        "receiverName": "AD",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17723165167"
    },
    {
        "createTime": "2025-01-19T16:16:49",
        "id": 3965,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13827229923"
    },
    {
        "createTime": "2025-01-19T15:33:12",
        "id": 3964,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13812948408"
    },
    {
        "createTime": "2025-01-19T14:48:44",
        "id": 3963,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18911551551"
    },
    {
        "createTime": "2025-01-19T14:44:23",
        "id": 3962,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18985040512"
    },
    {
        "createTime": "2025-01-19T14:20:38",
        "id": 3961,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13841527066"
    },
    {
        "createTime": "2025-01-19T14:17:16",
        "id": 3960,
        "receiverAddr": "汕头市星湖城15栋1503",
        "receiverMobile": "13502946900",
        "receiverName": "陈岚",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13502946900"
    },
    {
        "createTime": "2025-01-19T14:16:52",
        "id": 3959,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18910567391"
    },
    {
        "createTime": "2025-01-19T14:12:07",
        "id": 3958,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13290652308"
    },
    {
        "createTime": "2025-01-19T14:03:34",
        "id": 3957,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院十三号楼二单元1002",
        "receiverMobile": "15303164190",
        "receiverName": "蔡宗伯",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15303164190"
    },
    {
        "createTime": "2025-01-19T14:03:11",
        "id": 3956,
        "receiverAddr": "北京市朝阳区百子湾家园301-1-703",
        "receiverMobile": "13718962418",
        "receiverName": "杨凤云",
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610021314"
    },
    {
        "createTime": "2025-01-19T14:02:52",
        "id": 3955,
        "receiverAddr": "北京市朝阳区百子湾家园301-1-703",
        "receiverMobile": "13718962418",
        "receiverName": "杨凤云",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610021314"
    },
    {
        "createTime": "2025-01-19T14:01:01",
        "id": 3954,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-19T14:00:55",
        "id": 3953,
        "receiverAddr": "北京市朝阳区百子湾家园301-1-703",
        "receiverMobile": "13718962418",
        "receiverName": "杨凤云",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610021314"
    },
    {
        "createTime": "2025-01-19T14:00:37",
        "id": 3952,
        "receiverAddr": "北京市朝阳区百子湾家园301-1-703",
        "receiverMobile": "13718962418",
        "receiverName": "杨凤云",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610021314"
    },
    {
        "createTime": "2025-01-19T13:59:34",
        "id": 3951,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18033395642"
    },
    {
        "createTime": "2025-01-19T13:57:41",
        "id": 3950,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18033395642"
    },
    {
        "createTime": "2025-01-19T13:38:58",
        "id": 3949,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13996655930"
    },
    {
        "createTime": "2025-01-19T12:49:41",
        "id": 3948,
        "receiverAddr": "四川成都高新西区西源大道1313号成都合院1期",
        "receiverMobile": "19981208516",
        "receiverName": "耿浩然",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19981208516"
    },
    {
        "createTime": "2025-01-19T12:31:59",
        "id": 3947,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15768376735"
    },
    {
        "createTime": "2025-01-19T12:29:49",
        "id": 3946,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13823198510"
    },
    {
        "createTime": "2025-01-19T11:56:11",
        "id": 3945,
        "receiverAddr": "海南省三亚市吉阳区绿地悦澜湾1期4栋603",
        "receiverMobile": "13601302781",
        "receiverName": "常书义",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610111981"
    },
    {
        "createTime": "2025-01-19T11:55:58",
        "id": 3944,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18518015771"
    },
    {
        "createTime": "2025-01-19T11:25:05",
        "id": 3943,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13831635236"
    },
    {
        "createTime": "2025-01-19T11:21:26",
        "id": 3942,
        "receiverAddr": "北京市大兴区礼贤家园110地块13号楼2单元1602",
        "receiverMobile": "19223015670",
        "receiverName": "张岩",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19223015670"
    },
    {
        "createTime": "2025-01-19T11:10:56",
        "id": 3941,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18063645607"
    },
    {
        "createTime": "2025-01-19T11:10:48",
        "id": 3940,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18063645607"
    },
    {
        "createTime": "2025-01-19T11:10:21",
        "id": 3939,
        "receiverAddr": "北京市大兴区大兴国际机场东航倒班宿舍A1-619",
        "receiverMobile": "17301122829",
        "receiverName": "张成",
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17301122829"
    },
    {
        "createTime": "2025-01-19T11:05:08",
        "id": 3938,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18230126878"
    },
    {
        "createTime": "2025-01-19T11:03:53",
        "id": 3937,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院快递柜",
        "receiverMobile": "17611470823",
        "receiverName": "周思雯",
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15076215186"
    },
    {
        "createTime": "2025-01-19T10:45:46",
        "id": 3936,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-19T10:26:01",
        "id": 3935,
        "receiverAddr": "大兴区礼贤镇汇贤家园五里26号院",
        "receiverMobile": "18610040518",
        "receiverName": "张金华",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610040518"
    },
    {
        "createTime": "2025-01-19T10:18:35",
        "id": 3934,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18629577515"
    },
    {
        "createTime": "2025-01-19T10:02:53",
        "id": 3933,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13250649332"
    },
    {
        "createTime": "2025-01-19T09:34:25",
        "id": 3932,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13910201605"
    },
    {
        "createTime": "2025-01-19T09:25:58",
        "id": 3931,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院快递柜",
        "receiverMobile": "17611470823",
        "receiverName": "周思雯",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15076215186"
    },
    {
        "createTime": "2025-01-19T09:22:17",
        "id": 3930,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院快递柜",
        "receiverMobile": "17611470823",
        "receiverName": "周思雯",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15076215186"
    },
    {
        "createTime": "2025-01-19T09:10:16",
        "id": 3929,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13824773920"
    },
    {
        "createTime": "2025-01-19T08:24:37",
        "id": 3928,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15943811912"
    },
    {
        "createTime": "2025-01-19T08:02:56",
        "id": 3927,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18259169853"
    },
    {
        "createTime": "2025-01-19T08:02:15",
        "id": 3926,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13521313328"
    },
    {
        "createTime": "2025-01-19T00:18:51",
        "id": 3925,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13096805925"
    },
    {
        "createTime": "2025-01-18T21:41:32",
        "id": 3924,
        "receiverAddr": "北京市大兴区大兴机场生活服务中心",
        "receiverMobile": "18632132328",
        "receiverName": "孟宇辰",
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18632132328"
    },
    {
        "createTime": "2025-01-18T21:41:15",
        "id": 3923,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17371261980"
    },
    {
        "createTime": "2025-01-18T21:39:26",
        "id": 3922,
        "receiverAddr": "北京市大兴区大兴机场生活服务中心",
        "receiverMobile": "18632132328",
        "receiverName": "孟宇辰",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18632132328"
    },
    {
        "createTime": "2025-01-18T20:14:20",
        "id": 3921,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13967802420"
    },
    {
        "createTime": "2025-01-18T20:14:02",
        "id": 3920,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13701191252"
    },
    {
        "createTime": "2025-01-18T19:54:36",
        "id": 3919,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13652339618"
    },
    {
        "createTime": "2025-01-18T19:53:10",
        "id": 3918,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18511990411"
    },
    {
        "createTime": "2025-01-18T19:49:38",
        "id": 3917,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13585513791"
    },
    {
        "createTime": "2025-01-18T19:39:01",
        "id": 3916,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15810663503"
    },
    {
        "createTime": "2025-01-18T19:16:22",
        "id": 3915,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13424682920"
    },
    {
        "createTime": "2025-01-18T18:58:20",
        "id": 3914,
        "receiverAddr": "广州市越秀区明月路40号504",
        "receiverMobile": "18928921168",
        "receiverName": "罗小姐",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18928921168"
    },
    {
        "createTime": "2025-01-18T18:56:36",
        "id": 3913,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18722159911"
    },
    {
        "createTime": "2025-01-18T18:50:06",
        "id": 3912,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15861595440"
    },
    {
        "createTime": "2025-01-18T18:22:39",
        "id": 3911,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13599525166"
    },
    {
        "createTime": "2025-01-18T18:22:26",
        "id": 3910,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13599525166"
    },
    {
        "createTime": "2025-01-18T18:13:51",
        "id": 3909,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17330541187"
    },
    {
        "createTime": "2025-01-18T18:06:46",
        "id": 3908,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15702486610"
    },
    {
        "createTime": "2025-01-18T17:06:56",
        "id": 3907,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15705043966"
    },
    {
        "createTime": "2025-01-18T16:21:26",
        "id": 3906,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18618420556"
    },
    {
        "createTime": "2025-01-18T15:50:36",
        "id": 3905,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18239831259"
    },
    {
        "createTime": "2025-01-18T15:49:44",
        "id": 3904,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13853057203"
    },
    {
        "createTime": "2025-01-18T15:40:02",
        "id": 3903,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18059080086"
    },
    {
        "createTime": "2025-01-18T14:54:55",
        "id": 3902,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18383513800"
    },
    {
        "createTime": "2025-01-18T14:35:33",
        "id": 3901,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19312591592"
    },
    {
        "createTime": "2025-01-18T14:00:34",
        "id": 3900,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15075686635"
    },
    {
        "createTime": "2025-01-18T13:59:55",
        "id": 3899,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15631699239"
    },
    {
        "createTime": "2025-01-18T13:48:02",
        "id": 3898,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13539210890"
    },
    {
        "createTime": "2025-01-18T13:40:59",
        "id": 3897,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13924566433"
    },
    {
        "createTime": "2025-01-18T13:27:11",
        "id": 3896,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15210393055"
    },
    {
        "createTime": "2025-01-18T13:02:28",
        "id": 3895,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13981701915"
    },
    {
        "createTime": "2025-01-18T12:40:11",
        "id": 3894,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15897701610"
    },
    {
        "createTime": "2025-01-18T12:14:03",
        "id": 3893,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13725588327"
    },
    {
        "createTime": "2025-01-18T12:13:57",
        "id": 3892,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13779701956"
    },
    {
        "createTime": "2025-01-18T12:08:38",
        "id": 3891,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15810650749"
    },
    {
        "createTime": "2025-01-18T12:05:43",
        "id": 3890,
        "receiverAddr": "北京市大兴区大兴国际机场东航倒班宿舍A1-619",
        "receiverMobile": "17301122829",
        "receiverName": "张成",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17301122829"
    },
    {
        "createTime": "2025-01-18T12:03:54",
        "id": 3889,
        "receiverAddr": "北京市大兴区大兴国际机场东航倒班宿舍A1-619",
        "receiverMobile": "17301122829",
        "receiverName": "张成",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17301122829"
    },
    {
        "createTime": "2025-01-18T11:56:35",
        "id": 3888,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15810650749"
    },
    {
        "createTime": "2025-01-18T11:49:34",
        "id": 3887,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13521262999"
    },
    {
        "createTime": "2025-01-18T11:46:06",
        "id": 3886,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18893718235"
    },
    {
        "createTime": "2025-01-18T11:40:51",
        "id": 3885,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13631435987"
    },
    {
        "createTime": "2025-01-18T11:32:50",
        "id": 3884,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13631435987"
    },
    {
        "createTime": "2025-01-18T11:12:28",
        "id": 3883,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-18T11:06:22",
        "id": 3882,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15010862192"
    },
    {
        "createTime": "2025-01-18T11:02:11",
        "id": 3881,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15075686635"
    },
    {
        "createTime": "2025-01-18T11:01:25",
        "id": 3880,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18731601921"
    },
    {
        "createTime": "2025-01-18T10:35:11",
        "id": 3879,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13700234337"
    },
    {
        "createTime": "2025-01-18T10:15:09",
        "id": 3878,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13328285082"
    },
    {
        "createTime": "2025-01-18T10:12:40",
        "id": 3877,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18419904295"
    },
    {
        "createTime": "2025-01-18T09:49:05",
        "id": 3876,
        "receiverAddr": "浙江省舟山市定海区白泉镇弘业城市花园",
        "receiverMobile": "15168064958",
        "receiverName": "舒佳莹",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15168064958"
    },
    {
        "createTime": "2025-01-18T09:48:51",
        "id": 3875,
        "receiverAddr": "浙江省舟山市定海区白泉镇弘业城市花园",
        "receiverMobile": "15168064958",
        "receiverName": "舒佳莹",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15168064958"
    },
    {
        "createTime": "2025-01-18T08:54:43",
        "id": 3874,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15332262997"
    },
    {
        "createTime": "2025-01-18T08:48:51",
        "id": 3873,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19360239185"
    },
    {
        "createTime": "2025-01-18T06:26:26",
        "id": 3872,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13051532205"
    },
    {
        "createTime": "2025-01-18T06:26:08",
        "id": 3871,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13051532205"
    },
    {
        "createTime": "2025-01-18T06:25:39",
        "id": 3870,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13051532205"
    },
    {
        "createTime": "2025-01-18T06:24:16",
        "id": 3869,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13051532205"
    },
    {
        "createTime": "2025-01-17T20:35:24",
        "id": 3868,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18737263555"
    },
    {
        "createTime": "2025-01-17T20:27:22",
        "id": 3867,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18903212335"
    },
    {
        "createTime": "2025-01-17T18:59:37",
        "id": 3866,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "640107333"
    },
    {
        "createTime": "2025-01-17T18:32:18",
        "id": 3865,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13764326559"
    },
    {
        "createTime": "2025-01-17T18:23:29",
        "id": 3864,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18993710231"
    },
    {
        "createTime": "2025-01-17T18:22:16",
        "id": 3863,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15901292109"
    },
    {
        "createTime": "2025-01-17T18:21:51",
        "id": 3862,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13616067197"
    },
    {
        "createTime": "2025-01-17T17:36:35",
        "id": 3861,
        "receiverAddr": "内蒙古兴安盟阿尔山市圣泉小区18号楼",
        "receiverMobile": "13264349998",
        "receiverName": "张白雪",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13264349998"
    },
    {
        "createTime": "2025-01-17T16:18:23",
        "id": 3860,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13675747603"
    },
    {
        "createTime": "2025-01-17T16:18:20",
        "id": 3859,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13910013360"
    },
    {
        "createTime": "2025-01-17T16:13:45",
        "id": 3858,
        "receiverAddr": "甘肃省白银市白银区工农路金色华府",
        "receiverMobile": "18089438442",
        "receiverName": "冯彦俊",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18089438442"
    },
    {
        "createTime": "2025-01-17T16:06:56",
        "id": 3857,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15386303569"
    },
    {
        "createTime": "2025-01-17T16:06:32",
        "id": 3856,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13893267040"
    },
    {
        "createTime": "2025-01-17T16:02:24",
        "id": 3855,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13893267040"
    },
    {
        "createTime": "2025-01-17T15:50:05",
        "id": 3854,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13673181716"
    },
    {
        "createTime": "2025-01-17T15:40:59",
        "id": 3853,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17622797831"
    },
    {
        "createTime": "2025-01-17T15:36:19",
        "id": 3852,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18311491845"
    },
    {
        "createTime": "2025-01-17T15:12:34",
        "id": 3851,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15870139850"
    },
    {
        "createTime": "2025-01-17T14:58:14",
        "id": 3850,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15870139850"
    },
    {
        "createTime": "2025-01-17T14:51:01",
        "id": 3849,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13519803363"
    },
    {
        "createTime": "2025-01-17T14:33:17",
        "id": 3848,
        "receiverAddr": "北京市门头沟区永定镇中铁西城南区6-2-702",
        "receiverMobile": "13810428333",
        "receiverName": "尚先生",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13810428333"
    },
    {
        "createTime": "2025-01-17T14:28:03",
        "id": 3847,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18275815215"
    },
    {
        "createTime": "2025-01-17T14:14:46",
        "id": 3846,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13663268801"
    },
    {
        "createTime": "2025-01-17T14:13:06",
        "id": 3845,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15104831234"
    },
    {
        "createTime": "2025-01-17T14:01:51",
        "id": 3844,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_WAIT",
        "statusVal": 1,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-17T13:53:58",
        "id": 3843,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18602748710"
    },
    {
        "createTime": "2025-01-17T13:53:40",
        "id": 3842,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_WAIT",
        "statusVal": 1,
        "userMobile": "18602748710"
    },
    {
        "createTime": "2025-01-17T13:53:31",
        "id": 3841,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13548039707"
    },
    {
        "createTime": "2025-01-17T13:48:44",
        "id": 3840,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15213048479"
    },
    {
        "createTime": "2025-01-17T13:43:01",
        "id": 3839,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13146783428"
    },
    {
        "createTime": "2025-01-17T13:35:51",
        "id": 3838,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18007311332"
    },
    {
        "createTime": "2025-01-17T13:26:22",
        "id": 3837,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15888755655"
    },
    {
        "createTime": "2025-01-17T13:25:11",
        "id": 3836,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13871591201"
    },
    {
        "createTime": "2025-01-17T13:20:42",
        "id": 3835,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15700979363"
    },
    {
        "createTime": "2025-01-17T12:52:10",
        "id": 3834,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13609681365"
    },
    {
        "createTime": "2025-01-17T12:49:44",
        "id": 3833,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13901199819"
    },
    {
        "createTime": "2025-01-17T12:43:15",
        "id": 3832,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15201537783"
    },
    {
        "createTime": "2025-01-17T12:43:15",
        "id": 3831,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15201537783"
    },
    {
        "createTime": "2025-01-17T12:42:31",
        "id": 3830,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15110118709"
    },
    {
        "createTime": "2025-01-17T12:35:35",
        "id": 3829,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15814469688"
    },
    {
        "createTime": "2025-01-17T12:24:52",
        "id": 3828,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13911925593"
    },
    {
        "createTime": "2025-01-17T12:21:59",
        "id": 3827,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13086932416"
    },
    {
        "createTime": "2025-01-17T12:08:15",
        "id": 3826,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18620323772"
    },
    {
        "createTime": "2025-01-17T11:57:38",
        "id": 3825,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15810521866"
    },
    {
        "createTime": "2025-01-17T11:52:22",
        "id": 3824,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13552927207"
    },
    {
        "createTime": "2025-01-17T11:40:08",
        "id": 3823,
        "receiverAddr": "上海市浦东新区广丹路222弄9栋",
        "receiverMobile": "18521510276",
        "receiverName": "张杰",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18521510276"
    },
    {
        "createTime": "2025-01-17T11:11:27",
        "id": 3822,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15201346288"
    },
    {
        "createTime": "2025-01-17T11:02:34",
        "id": 3821,
        "receiverAddr": "北京市朝阳区百子湾家园301-1-703",
        "receiverMobile": "13718962418",
        "receiverName": "杨凤云",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610021314"
    },
    {
        "createTime": "2025-01-17T11:01:15",
        "id": 3820,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-17T10:53:41",
        "id": 3819,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13911133644"
    },
    {
        "createTime": "2025-01-17T09:53:19",
        "id": 3818,
        "receiverAddr": "大兴区礼贤镇汇贤家园五里26号院",
        "receiverMobile": "18610040518",
        "receiverName": "张金华",
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610040518"
    },
    {
        "createTime": "2025-01-17T08:00:37",
        "id": 3817,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-17T07:24:06",
        "id": 3816,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15566789073"
    },
    {
        "createTime": "2025-01-17T07:07:38",
        "id": 3815,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15774355957"
    },
    {
        "createTime": "2025-01-16T22:31:43",
        "id": 3814,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13141257521"
    },
    {
        "createTime": "2025-01-16T22:31:35",
        "id": 3813,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13141257521"
    },
    {
        "createTime": "2025-01-16T21:04:34",
        "id": 3812,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13552482940"
    },
    {
        "createTime": "2025-01-16T20:50:29",
        "id": 3811,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13919280110"
    },
    {
        "createTime": "2025-01-16T20:47:30",
        "id": 3810,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_WAIT",
        "statusVal": 1,
        "userMobile": "18209312642"
    },
    {
        "createTime": "2025-01-16T19:40:57",
        "id": 3809,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-16T19:39:34",
        "id": 3808,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardDelivery": {
            "dvyFlowId": "已现场发放",
            "dvyId": 2647,
            "id": 1,
            "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
            "receiverMobile": "15600090555",
            "receiverName": "卢青松",
            "ttlId": 3,
            "ttlRewardId": 93,
            "ttlRewardRecordId": 3808,
            "ttlUserRecordId": 9777
        },
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-16T19:38:39",
        "id": 3807,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18480808816"
    },
    {
        "createTime": "2025-01-16T19:38:09",
        "id": 3806,
        "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
        "receiverMobile": "15600090555",
        "receiverName": "卢青松",
        "rewardDelivery": {
            "dvyFlowId": "已现场发放",
            "dvyId": 2647,
            "id": 2,
            "receiverAddr": "北京市大兴区西红门同兴园小区5号楼三单元403",
            "receiverMobile": "15600090555",
            "receiverName": "卢青松",
            "ttlId": 3,
            "ttlRewardId": 93,
            "ttlRewardRecordId": 3806,
            "ttlUserRecordId": 9777
        },
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600090555"
    },
    {
        "createTime": "2025-01-16T18:34:43",
        "id": 3805,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13682492808"
    },
    {
        "createTime": "2025-01-16T18:31:13",
        "id": 3804,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15874006879"
    },
    {
        "createTime": "2025-01-16T18:10:22",
        "id": 3803,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15949648344"
    },
    {
        "createTime": "2025-01-16T18:02:19",
        "id": 3802,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13962672323"
    },
    {
        "createTime": "2025-01-16T17:56:23",
        "id": 3801,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13537798422"
    },
    {
        "createTime": "2025-01-16T17:51:29",
        "id": 3800,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13587773346"
    },
    {
        "createTime": "2025-01-16T17:20:21",
        "id": 3799,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17865569730"
    },
    {
        "createTime": "2025-01-16T17:13:45",
        "id": 3798,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17865569730"
    },
    {
        "createTime": "2025-01-16T17:01:17",
        "id": 3797,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18695119900"
    },
    {
        "createTime": "2025-01-16T16:19:43",
        "id": 3796,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13278883524"
    },
    {
        "createTime": "2025-01-16T16:17:33",
        "id": 3795,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19239161818"
    },
    {
        "createTime": "2025-01-16T15:56:25",
        "id": 3794,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13925262864"
    },
    {
        "createTime": "2025-01-16T15:52:55",
        "id": 3793,
        "receiverAddr": "北京市朝阳区东润枫景2期",
        "receiverMobile": "15810371704",
        "receiverName": "秦婉婉",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15810371704"
    },
    {
        "createTime": "2025-01-16T15:25:33",
        "id": 3792,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19239161818"
    },
    {
        "createTime": "2025-01-16T14:57:39",
        "id": 3791,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18200807331"
    },
    {
        "createTime": "2025-01-16T14:48:54",
        "id": 3790,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15210819932"
    },
    {
        "createTime": "2025-01-16T14:32:32",
        "id": 3789,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13901060104"
    },
    {
        "createTime": "2025-01-16T14:30:39",
        "id": 3788,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13699178473"
    },
    {
        "createTime": "2025-01-16T14:22:03",
        "id": 3787,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13632111785"
    },
    {
        "createTime": "2025-01-16T14:21:00",
        "id": 3786,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13632111380"
    },
    {
        "createTime": "2025-01-16T14:15:29",
        "id": 3785,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17807311713"
    },
    {
        "createTime": "2025-01-16T14:02:21",
        "id": 3784,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-16T14:02:06",
        "id": 3783,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-16T14:01:50",
        "id": 3782,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13699178473"
    },
    {
        "createTime": "2025-01-16T14:00:53",
        "id": 3781,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-16T14:00:45",
        "id": 3780,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-16T14:00:35",
        "id": 3779,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-16T13:18:56",
        "id": 3778,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17322589561"
    },
    {
        "createTime": "2025-01-16T12:34:13",
        "id": 3777,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18263107277"
    },
    {
        "createTime": "2025-01-16T12:26:37",
        "id": 3776,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18800063630"
    },
    {
        "createTime": "2025-01-16T12:10:53",
        "id": 3775,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18813001689"
    },
    {
        "createTime": "2025-01-16T11:46:38",
        "id": 3774,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13845988640"
    },
    {
        "createTime": "2025-01-16T11:31:17",
        "id": 3773,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18825212207"
    },
    {
        "createTime": "2025-01-16T11:22:00",
        "id": 3772,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13810028941"
    },
    {
        "createTime": "2025-01-16T11:19:13",
        "id": 3771,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17343885890"
    },
    {
        "createTime": "2025-01-16T11:19:05",
        "id": 3770,
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17343885890"
    },
    {
        "createTime": "2025-01-16T11:13:22",
        "id": 3769,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15771737035"
    },
    {
        "createTime": "2025-01-16T11:13:14",
        "id": 3768,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15277144802"
    },
    {
        "createTime": "2025-01-16T10:29:10",
        "id": 3767,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17767144163"
    },
    {
        "createTime": "2025-01-16T10:05:46",
        "id": 3766,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19535789212"
    },
    {
        "createTime": "2025-01-16T09:54:17",
        "id": 3765,
        "receiverAddr": "福建省泉州市石狮市八七路世纪家园",
        "receiverMobile": "13675998737",
        "receiverName": "黄蓉",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13675998737"
    },
    {
        "createTime": "2025-01-16T09:20:29",
        "id": 3764,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13260371536"
    },
    {
        "createTime": "2025-01-16T09:10:16",
        "id": 3763,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18045864858"
    },
    {
        "createTime": "2025-01-16T08:56:46",
        "id": 3762,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院十三号楼二单元1002",
        "receiverMobile": "15303164190",
        "receiverName": "蔡宗伯",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15303164190"
    },
    {
        "createTime": "2025-01-16T08:56:37",
        "id": 3761,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院十三号楼二单元1002",
        "receiverMobile": "15303164190",
        "receiverName": "蔡宗伯",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15303164190"
    },
    {
        "createTime": "2025-01-16T08:27:13",
        "id": 3760,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-16T06:42:03",
        "id": 3759,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-16T06:39:39",
        "id": 3758,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    },
    {
        "createTime": "2025-01-16T06:38:22",
        "id": 3757,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15989688013"
    },
    {
        "createTime": "2025-01-15T19:46:50",
        "id": 3756,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15731497510"
    },
    {
        "createTime": "2025-01-15T19:27:38",
        "id": 3755,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19535789212"
    },
    {
        "createTime": "2025-01-15T19:11:57",
        "id": 3754,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15130327690"
    },
    {
        "createTime": "2025-01-15T18:52:59",
        "id": 3753,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15081351928"
    },
    {
        "createTime": "2025-01-15T18:48:16",
        "id": 3752,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18678881935"
    },
    {
        "createTime": "2025-01-15T18:23:26",
        "id": 3751,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15943079607"
    },
    {
        "createTime": "2025-01-15T18:17:50",
        "id": 3750,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13861812223"
    },
    {
        "createTime": "2025-01-15T18:07:00",
        "id": 3749,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15516305818"
    },
    {
        "createTime": "2025-01-15T18:04:39",
        "id": 3748,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13907045211"
    },
    {
        "createTime": "2025-01-15T17:54:05",
        "id": 3747,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13904906606"
    },
    {
        "createTime": "2025-01-15T17:53:26",
        "id": 3746,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13556973995"
    },
    {
        "createTime": "2025-01-15T16:53:08",
        "id": 3745,
        "receiverAddr": "北京市朝阳区百子湾家园301-1-703",
        "receiverMobile": "13718962418",
        "receiverName": "杨凤云",
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18610021314"
    },
    {
        "createTime": "2025-01-15T16:42:18",
        "id": 3744,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18500904801"
    },
    {
        "createTime": "2025-01-15T16:05:04",
        "id": 3743,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15925676825"
    },
    {
        "createTime": "2025-01-15T15:49:31",
        "id": 3742,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18858851574"
    },
    {
        "createTime": "2025-01-15T15:42:50",
        "id": 3741,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15075686635"
    },
    {
        "createTime": "2025-01-15T15:39:30",
        "id": 3740,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "19581539260"
    },
    {
        "createTime": "2025-01-15T15:22:04",
        "id": 3739,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15118760338"
    },
    {
        "createTime": "2025-01-15T15:10:48",
        "id": 3738,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18850562889"
    },
    {
        "createTime": "2025-01-15T14:34:01",
        "id": 3737,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13141257521"
    },
    {
        "createTime": "2025-01-15T14:34:01",
        "id": 3736,
        "rewardName": "小大董-冰粉（价值12元）一份",
        "rewardPic": "2025/01/a149ffbca1404e629229a7b77a7191b9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1074",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13141257521"
    },
    {
        "createTime": "2025-01-15T14:29:47",
        "id": 3735,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18716505182"
    },
    {
        "createTime": "2025-01-15T14:03:14",
        "id": 3734,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院十三号楼二单元1002",
        "receiverMobile": "15303164190",
        "receiverName": "蔡宗伯",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15303164190"
    },
    {
        "createTime": "2025-01-15T14:03:05",
        "id": 3733,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院十三号楼二单元1002",
        "receiverMobile": "15303164190",
        "receiverName": "蔡宗伯",
        "rewardName": "兴先送100积分",
        "rewardPic": "https://alyoss.portus.cn//2025/01/e1164619f7d64a9e85f6d93cfd103031.png",
        "rewardType": "POINT",
        "rewardTypeVal": 1,
        "rewardVal": "100",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15303164190"
    },
    {
        "createTime": "2025-01-15T14:02:57",
        "id": 3732,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院十三号楼二单元1002",
        "receiverMobile": "15303164190",
        "receiverName": "蔡宗伯",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15303164190"
    },
    {
        "createTime": "2025-01-15T14:02:52",
        "id": 3731,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院十三号楼二单元1002",
        "receiverMobile": "15303164190",
        "receiverName": "蔡宗伯",
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15303164190"
    },
    {
        "createTime": "2025-01-15T14:02:45",
        "id": 3730,
        "receiverAddr": "北京市大兴区榆垡镇空港新苑一号院十三号楼二单元1002",
        "receiverMobile": "15303164190",
        "receiverName": "蔡宗伯",
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15303164190"
    },
    {
        "createTime": "2025-01-15T14:02:11",
        "id": 3729,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18419904295"
    },
    {
        "createTime": "2025-01-15T13:16:57",
        "id": 3728,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15534948109"
    },
    {
        "createTime": "2025-01-15T13:06:45",
        "id": 3727,
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17611275557"
    },
    {
        "createTime": "2025-01-15T12:50:36",
        "id": 3726,
        "receiverAddr": "贵州省黔西南州兴义市桔园华府",
        "receiverMobile": "18428327860",
        "receiverName": "苏云婷",
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "18428327860"
    },
    {
        "createTime": "2025-01-15T12:47:15",
        "id": 3725,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17696509584"
    },
    {
        "createTime": "2025-01-15T12:46:46",
        "id": 3724,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13439941109"
    },
    {
        "createTime": "2025-01-15T12:15:51",
        "id": 3723,
        "rewardName": "中联航兑换券盲盒（往返随心飞机票、随心飞50元兑换券、随心飞20元兑换券、行李20元兑换券）一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13522462312"
    },
    {
        "createTime": "2025-01-15T11:49:33",
        "id": 3722,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17611275557"
    },
    {
        "createTime": "2025-01-15T11:49:20",
        "id": 3721,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17611275557"
    },
    {
        "createTime": "2025-01-15T11:49:08",
        "id": 3720,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17611275557"
    },
    {
        "createTime": "2025-01-15T11:43:42",
        "id": 3719,
        "receiverAddr": "北京市房山区青龙湖镇宜青街2号院15号楼1单元602",
        "receiverMobile": "15600907717",
        "receiverName": "于会娇",
        "rewardName": "棒约翰-周边钥匙链一个",
        "rewardPic": "https://alyoss.portus.cn//2025/01/14625b053d324ec78e8b8db494a79f4a.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15600907717"
    },
    {
        "createTime": "2025-01-15T11:36:52",
        "id": 3718,
        "rewardName": "小吊梨汤-菜团子（价值6元）一份 ",
        "rewardPic": "https://alyoss.portus.cn//2025/01/81039ed9c3c24779827075d2371b4281.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1066",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13269633675"
    },
    {
        "createTime": "2025-01-15T11:36:27",
        "id": 3717,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13269633675"
    },
    {
        "createTime": "2025-01-15T10:43:26",
        "id": 3716,
        "rewardName": "老舍茶馆-杏仁豆腐牛乳茶（价值26元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/825aa229e1aa4532b15bd51a89d2b901.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1062",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15965313676"
    },
    {
        "createTime": "2025-01-15T10:38:56",
        "id": 3715,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13953166258"
    },
    {
        "createTime": "2025-01-15T10:12:28",
        "id": 3714,
        "receiverAddr": "兰州市安宁区十里店街道蓝星花园",
        "receiverMobile": "17793211668",
        "receiverName": "汪明珠",
        "rewardName": "中联航单次即开随机飞往返机票兑换券一张",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "17793211668"
    },
    {
        "createTime": "2025-01-15T09:55:27",
        "id": 3713,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15218811183"
    },
    {
        "createTime": "2025-01-15T09:08:38",
        "id": 3712,
        "rewardName": "无敌家-可尔必思（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/161010aedc5f4654a5d65a0b3a01936e.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1067",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15901312636"
    },
    {
        "createTime": "2025-01-15T09:08:26",
        "id": 3711,
        "rewardName": "松鹤楼-细豆沙圆子（价值22元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/87b386638aca4956bfa24f50b68b47f9.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1064",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15201010720"
    },
    {
        "createTime": "2025-01-15T09:01:33",
        "id": 3710,
        "receiverAddr": "山东潍坊安丘江山一品29号楼西单元",
        "receiverMobile": "15069672588",
        "receiverName": "赵妍姿",
        "rewardName": "中联航单次即开随机飞往返机票兑换券一张",
        "rewardPic": "https://alyoss.portus.cn//2025/01/4e37d1ae11854efda0e12a122a5e83a9.png",
        "rewardType": "PHYSICAL",
        "rewardTypeVal": 6,
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "15069672588"
    },
    {
        "createTime": "2025-01-15T08:01:09",
        "id": 3709,
        "receiverAddr": "北京市大兴区榆垡镇太子务村1/1",
        "receiverMobile": "13520822515",
        "receiverName": "宋伟萍",
        "rewardName": "苏面坊-青菜（价值6元）一份",
        "rewardPic": "https://alyoss.portus.cn//2025/01/dd1f83366c7841dd8dc3d1abee56f34b.png",
        "rewardType": "COUPON",
        "rewardTypeVal": 0,
        "rewardVal": "1063",
        "status": "AUTO_DONE",
        "statusVal": 2,
        "userMobile": "13520822515"
    }
]

# Map status values to meaningful text
status_mapping = {
    "NO_NEED_AUTO": "无需自动发放",
    "AUTO_WAIT": "未自动发放",
    "AUTO_DONE": "已自动发放",
    "AUTO_FAIL": "自动发放失败"
}

# Parse and process data
data_to_export = []
for entry in json_data:
    data_to_export.append({
        "userMobile": entry["userMobile"],
        "rewardName": entry["rewardName"],
        "createTime": entry["createTime"],
        "status": status_mapping.get(entry["status"], entry["status"]),
        "failReason": entry.get("failReason"),
        "receiverName": entry.get("receiverName"),
        "receiverAddr": entry.get("receiverAddr"),
        "receiverMobile": entry.get("receiverMobile")
    })

# Create a DataFrame
df = pd.DataFrame(data_to_export)
print(len(json_data))
print(len(df))
# Export to Excel
output_file = "D://chromedownload//reward_data.xlsx"
df.to_excel(output_file, index=False, engine="openpyxl")

print(f"Data exported to {output_file}")
